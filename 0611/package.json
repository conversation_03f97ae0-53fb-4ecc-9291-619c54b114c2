{"name": "5gweb", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "ESLINT_NO_DEV_ERRORS=true next build", "start": "next start", "lint": "next lint", "prisma:seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts", "import-historical-data": "ts-node scripts/import-historical-data.ts"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^1.0.0", "@casl/ability": "^6.7.3", "@casl/prisma": "^1.5.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.3.4", "@prisma/client": "^5.11.0", "@prisma/extension-accelerate": "^1.3.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-table": "^8.13.2", "@tiptap/extension-blockquote": "^2.11.7", "@tiptap/extension-bubble-menu": "^2.11.7", "@tiptap/extension-code-block": "^2.11.7", "@tiptap/extension-color": "^2.11.7", "@tiptap/extension-floating-menu": "^2.11.7", "@tiptap/extension-highlight": "^2.11.7", "@tiptap/extension-horizontal-rule": "^2.11.7", "@tiptap/extension-image": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-table": "^2.11.7", "@tiptap/extension-table-cell": "^2.11.7", "@tiptap/extension-table-header": "^2.11.7", "@tiptap/extension-table-row": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-typography": "^2.11.7", "@tiptap/extension-underline": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@tremor/react": "^3.18.7", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.9", "@types/nodemailer": "^6.4.17", "area-data": "^5.0.6", "bcryptjs": "^2.4.3", "casbin": "^5.38.0", "casbin-prisma-adapter": "^1.7.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.1.1", "crypto-js": "^4.2.0", "csv-stringify": "^6.5.2", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "ioredis": "^5.6.0", "jose": "^6.0.10", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lru-cache": "^11.1.0", "lucide-react": "^0.358.0", "next": "14.1.3", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "nodemailer": "^6.10.0", "process": "^0.11.10", "react": "^18.2.0", "react-day-picker": "^9.6.4", "react-dom": "^18.2.0", "react-hook-form": "^7.51.0", "react-hot-toast": "^2.5.2", "react-quill": "^2.0.0", "recharts": "^2.15.2", "sharp": "^0.34.1", "sonner": "^1.4.3", "swr": "^2.3.3", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.3", "ueditor-react": "1.0.1", "uuid": "^11.1.0", "yup": "^1.6.1", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20.17.30", "@types/react": "^18.3.20", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.3", "postcss": "^8", "prisma": "^5.11.0", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "typescript": "^5.8.2"}}