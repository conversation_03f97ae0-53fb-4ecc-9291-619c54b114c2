import logger from '@/lib/utils/logger';

import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import * as jose from 'jose'
import { getToken } from "next-auth/jwt"
import { v4 as uuidv4 } from 'uuid'

const JWT_SECRET = new TextEncoder().encode(
  process.env.NEXTAUTH_SECRET || process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

// 需要认证的路径
const AUTH_PATHS = [
  "/api/user/profile",
  "/api/user/notifications/settings",
  "/api/user/login-history",
  "/api/customers",
  "/api/notifications/unread-count",
  "/user",
  "/user/profile",
  "/user/settings",
  "/account-profile",  // 个人资料路径
  "/user/history",
  "/dashboard"
]

// 需要认证的布局路径
const AUTH_LAYOUTS = [
  "/(dashboard)",
  "/user"
]

// 需要权限检查的API路径
const PROTECTED_PATHS = [
  "/api/resources",
  "/api/user/profile"
]

// 白名单路径
const PUBLIC_PATHS = [
  "/login",
  "/register",
  "/forgot-password",
  "/api/auth",  // 所有NextAuth相关路径
  "/_next",
  "/favicon.ico",
  "/static",
  "/images",
  "/fonts",
  "/api/auth/me",
  "/api/auth/callback",
  "/api/auth/check-permission",
  "/api/auth/csrf",
  "/api/auth/session"
]

// 生成唯一请求ID用于日志跟踪
function generateRequestId() {
  return uuidv4().substring(0, 8);
}

// 获取认证信息
async function getAuthInfo(request: NextRequest, requestId: string) {
  try {
    // 1. 首先尝试获取 NextAuth session
    const sessionToken = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET
    })

    logger.debug(`[中间件:${requestId}] NextAuth 会话令牌:`, sessionToken)

    if (sessionToken) {
      // 使用id字段作为用户ID
      const userId = sessionToken.id || sessionToken.sub
      if (userId) {
        logger.debug(`[中间件:${requestId}] 找到 NextAuth 会话, 用户ID: ${userId}`)
        return {
          isAuthenticated: true,
          userId: userId as string,
          roleCode: sessionToken.roleCode as string,
          permissions: sessionToken.permissions as string[],
          authType: 'nextauth'
        }
      }
    }

    // 2. 如果没有 NextAuth session，尝试获取和验证 JWT token
    const token = request.cookies.get("token")?.value

    if (!token) {
      logger.debug(`[中间件:${requestId}] 未找到任何认证令牌`)
      return { isAuthenticated: false, authType: 'none' }
    }

    try {
      const { payload } = await jose.jwtVerify(token, JWT_SECRET)
      if (!payload.sub) {
        logger.warn(`[中间件:${requestId}] JWT令牌无效，缺少 sub 字段`)
        return { isAuthenticated: false, authType: 'invalid' }
      }

      logger.debug(`[中间件:${requestId}] JWT令牌有效, 用户ID: ${payload.sub}`)
      return {
        isAuthenticated: true,
        userId: payload.sub as string,
        roleCode: payload.role as string,
        permissions: payload.permissions as string[] || [],
        authType: 'jwt'
      }
    } catch (error) {
      logger.error(`[中间件:${requestId}] JWT Token验证失败:`, error)
      return { isAuthenticated: false, authType: 'expired' }
    }
  } catch (error) {
    logger.error(`[中间件:${requestId}] 认证检查失败:`, error)
    return { isAuthenticated: false, authType: 'error' }
  }
}

// 验证重定向路径安全性
function validateRedirectUrl(url: string): boolean {
  // 如果URL为空或者undefined，视为不安全
  if (!url) return false;

  // 检查是否是相对URL（不包含协议与域名）
  if (url.startsWith('http://') || url.startsWith('https://')) {
    // 如果是绝对URL，验证是否是本站域名
    try {
      const urlObj = new URL(url);
      // 检查域名是否与当前站点相同
      const hostname = process.env.NEXTAUTH_URL
        ? new URL(process.env.NEXTAUTH_URL).hostname
        : 'localhost';

      return urlObj.hostname === hostname;
    } catch (e) {
      return false;
    }
  }

  // 验证相对URL是否从根路径开始且不包含特殊字符
  return url.startsWith('/') &&
    !url.includes('\\') &&
    !url.includes('%') &&
    !(/[<>"']/).test(url);
}

// 检查用户是否有指定权限
async function checkUserPermission(userId: string, resource: string, action: string, requestId: string) {
  try {
    // 这里可以集成jCasbin进行权限验证
    // 为了简单演示，我们先使用基本判断
    logger.debug(`[中间件:${requestId}] 检查权限: 用户=${userId}, 资源=${resource}, 操作=${action}`)

    // 实际生产环境应该使用jCasbin进行检查
    // 例如: const allowed = await enforcer.enforce(userId, resource, action);
    return true; // 临时默认允许，后续需要接入jCasbin
  } catch (error) {
    logger.error(`[中间件:${requestId}] 权限检查失败:`, error);
    return false;
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const requestId = generateRequestId()
  logger.debug(`[中间件:${requestId}] 处理请求: ${pathname}`)

  // 安全验证重定向URL参数
  if (request.nextUrl.searchParams.has('returnUrl')) {
    const returnUrl = request.nextUrl.searchParams.get('returnUrl') as string
    if (!validateRedirectUrl(returnUrl)) {
      logger.warn(`[中间件:${requestId}] 检测到不安全的重定向URL: ${returnUrl}`)

      // 删除不安全的returnUrl参数
      const safeUrl = new URL(request.url)
      safeUrl.searchParams.delete('returnUrl')

      // 返回安全的URL
      return NextResponse.redirect(safeUrl)
    }
  }

  // 检查是否是白名单路径
  if (PUBLIC_PATHS.some(path => pathname.startsWith(path))) {
    logger.debug(`[中间件:${requestId}] 公开路径: ${pathname}, 允许访问`)
    return NextResponse.next()
  }

  // 检查是否需要认证
  const requiresAuth = AUTH_PATHS.some(path => pathname.startsWith(path)) ||
                      AUTH_LAYOUTS.some(layout => pathname.startsWith(layout))
  const needsPermission = PROTECTED_PATHS.some(path => pathname.startsWith(path))

  if (!requiresAuth && !needsPermission) {
    logger.debug(`[中间件:${requestId}] 无需认证路径: ${pathname}, 允许访问`)
    return NextResponse.next()
  }

  try {
    const authInfo = await getAuthInfo(request, requestId)

    if (!authInfo.isAuthenticated) {
      logger.debug(`[中间件:${requestId}] 未认证访问: ${pathname}, 认证类型: ${authInfo.authType}`)
      const isApiRequest = pathname.startsWith('/api/')

      if (isApiRequest) {
        return NextResponse.json({
          success: false,
          message: "未授权访问",
          requestId
        }, { status: 401 })
      }

      // 保存当前URL作为returnUrl（安全检查已在前面完成）
      const url = new URL("/login", request.url)
      url.searchParams.set("returnUrl", pathname)

      // 添加一次性令牌防止CSRF
      const csrfToken = uuidv4().substring(0, 16)
      url.searchParams.set("csrf", csrfToken)

      logger.debug(`[中间件:${requestId}] 重定向到登录页: ${url.toString()}`)
      return NextResponse.redirect(url)
    }

    // 为API请求添加用户信息到请求头
    const requestHeaders = new Headers(request.headers)
    requestHeaders.set('x-user-id', authInfo.userId)
    requestHeaders.set('x-user-role', authInfo.roleCode)
    requestHeaders.set('x-request-id', requestId)
    requestHeaders.set('x-auth-type', authInfo.authType)

    if (authInfo.permissions) {
      requestHeaders.set('x-user-permissions', JSON.stringify(authInfo.permissions))
    }

    // 如果是需要权限检查的路径，检查用户权限
    if (needsPermission) {
      const resource = pathname.split('/').pop() || ''
      const hasPermission = await checkUserPermission(
        authInfo.userId,
        resource,
        'access',
        requestId
      )

      if (!hasPermission) {
        logger.warn(`[中间件:${requestId}] 权限不足: 用户=${authInfo.userId}, 资源=${resource}`)
        return NextResponse.json({
          success: false,
          message: "无权访问此资源",
          requestId
        }, { status: 403 })
      }
    }

    logger.debug(`[中间件:${requestId}] 认证成功: ${pathname}, 用户=${authInfo.userId}`)

    // 继续请求，带上用户信息
    return NextResponse.next({
      request: {
        headers: requestHeaders
      }
    })
  } catch (error) {
    logger.error(`[中间件:${requestId}] 处理错误:`, error)
    return NextResponse.json({
      success: false,
      message: "服务器内部错误",
      requestId
    }, { status: 500 })
  }
}

export const config = {
  matcher: [
    /*
     * 匹配所有路径，除了:
     * - API路由中的NextAuth路由
     * - 静态文件路由
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|logo.png|logo.svg|placeholder|avatars|icons|uploads|templates|manifest.json|sw.js|login|register|forgot-password).*)',
  ],
}