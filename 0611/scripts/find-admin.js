// 查询管理员用户脚本
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function findAdminUsers() {
  try {
    // 查询角色为管理员的用户
    const adminUsers = await prisma.user.findMany({
      where: {
        OR: [
          { roleCode: 'admin' },
          { roleCode: 'ADMIN' },
          { roleCode: 'Admin' },
          { roleCode: 'administrator' },
          { roleCode: 'Administrator' },
          { roleCode: 'ADMINISTRATOR' },
          { roleCode: 'super' },
          { roleCode: 'Super' },
          { roleCode: 'SUPER' },
          { roleCode: 'superadmin' },
          { roleCode: 'SuperAdmin' },
          { roleCode: 'SUPERADMIN' },
          { roleCode: 'root' },
          { roleCode: 'Root' },
          { roleCode: 'ROOT' },
        ]
      },
      select: {
        id: true,
        username: true,
        email: true,
        password: true,
        roleCode: true,
        status: true,
        createdAt: true,
      }
    });

    console.log('找到管理员用户:');
    console.log(JSON.stringify(adminUsers, null, 2));

    // 查询所有角色
    const roles = await prisma.role.findMany();
    console.log('\n系统中的所有角色:');
    console.log(JSON.stringify(roles, null, 2));

    // 查询所有用户
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        password: true,
        roleCode: true,
        status: true,
      }
    });

    console.log('\n系统中的所有用户:');
    console.log(JSON.stringify(allUsers, null, 2));

  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

findAdminUsers();
