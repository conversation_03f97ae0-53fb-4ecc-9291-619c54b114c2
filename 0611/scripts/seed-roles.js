// 初始化角色数据的脚本
const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function main() {
  try {
    // 创建管理员角色
    const adminRole = await prisma.role.upsert({
      where: { code: 'ADMIN' },
      update: {},
      create: {
        code: 'ADMIN',
        name: '管理员',
        type: 'system',
        description: '系统管理员',
        permissions: ['*'],
        notificationEnabled: true
      }
    })
    console.log('管理员角色创建成功:', adminRole)

    // 创建普通用户角色
    const userRole = await prisma.role.upsert({
      where: { code: 'USER' },
      update: {},
      create: {
        code: 'USER',
        name: '普通用户',
        type: 'personal',
        description: '普通用户',
        permissions: ['TASK_READ', 'TASK_CREATE'],
        notificationEnabled: true
      }
    })
    console.log('普通用户角色创建成功:', userRole)

    // 创建企业用户角色
    const enterpriseRole = await prisma.role.upsert({
      where: { code: 'ENTERPRISE' },
      update: {},
      create: {
        code: 'ENTERPRISE',
        name: '企业用户',
        type: 'enterprise',
        description: '企业用户',
        permissions: ['TASK_READ', 'TASK_CREATE', 'TASK_MANAGE'],
        notificationEnabled: true
      }
    })
    console.log('企业用户角色创建成功:', enterpriseRole)

    console.log('角色数据初始化完成')
  } catch (error) {
    console.error('初始化角色数据失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
