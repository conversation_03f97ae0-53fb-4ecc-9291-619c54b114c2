// 初始化资源和操作数据的脚本
const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function main() {
  try {
    // 创建用户资源
    const usersResource = await prisma.resource.upsert({
      where: { code: 'users' },
      update: {},
      create: {
        code: 'users',
        name: '用户管理',
        type: 'menu',
        description: '用户管理资源'
      }
    })
    console.log('用户管理资源创建成功:', usersResource)

    // 创建费率资源
    const ratesResource = await prisma.resource.upsert({
      where: { code: 'rates' },
      update: {},
      create: {
        code: 'rates',
        name: '费率管理',
        type: 'menu',
        description: '费率管理资源'
      }
    })
    console.log('费率管理资源创建成功:', ratesResource)

    // 创建通知资源
    const notificationsResource = await prisma.resource.upsert({
      where: { code: 'notifications' },
      update: {},
      create: {
        code: 'notifications',
        name: '通知管理',
        type: 'menu',
        description: '通知管理资源'
      }
    })
    console.log('通知管理资源创建成功:', notificationsResource)

    // 创建系统设置资源
    const settingsResource = await prisma.resource.upsert({
      where: { code: 'settings' },
      update: {},
      create: {
        code: 'settings',
        name: '系统设置',
        type: 'menu',
        description: '系统设置资源'
      }
    })
    console.log('系统设置资源创建成功:', settingsResource)

    // 创建用户操作
    const userOperations = [
      {
        code: 'users:read',
        name: '查看用户',
        type: 'action',
        description: '查看用户列表和详情'
      },
      {
        code: 'users:create',
        name: '创建用户',
        type: 'action',
        description: '创建新用户'
      },
      {
        code: 'users:update',
        name: '更新用户',
        type: 'action',
        description: '更新用户信息'
      },
      {
        code: 'users:delete',
        name: '删除用户',
        type: 'action',
        description: '删除用户'
      }
    ]

    for (const op of userOperations) {
      const operation = await prisma.operation.upsert({
        where: { code: op.code },
        update: {},
        create: {
          code: op.code,
          name: op.name,
          type: op.type,
          description: op.description,
          enabled: true
        }
      })
      console.log(`用户操作 ${op.name} 创建成功:`, operation)
    }

    // 创建费率操作
    const ratesOperations = [
      {
        code: 'rates:read',
        name: '查看费率',
        type: 'action',
        description: '查看费率列表和详情'
      },
      {
        code: 'rates:create',
        name: '创建费率',
        type: 'action',
        description: '创建新费率'
      },
      {
        code: 'rates:update',
        name: '更新费率',
        type: 'action',
        description: '更新费率信息'
      },
      {
        code: 'rates:delete',
        name: '删除费率',
        type: 'action',
        description: '删除费率'
      },
      {
        code: 'rates:manage',
        name: '管理费率',
        type: 'action',
        description: '管理费率操作'
      }
    ]

    for (const op of ratesOperations) {
      const operation = await prisma.operation.upsert({
        where: { code: op.code },
        update: {},
        create: {
          code: op.code,
          name: op.name,
          type: op.type,
          description: op.description,
          enabled: true
        }
      })
      console.log(`费率操作 ${op.name} 创建成功:`, operation)
    }

    // 创建通知操作
    const notificationOperations = [
      {
        code: 'notifications:read',
        name: '查看通知',
        type: 'action',
        description: '查看通知列表和详情'
      },
      {
        code: 'notifications:create',
        name: '创建通知',
        type: 'action',
        description: '创建新通知'
      },
      {
        code: 'notifications:update',
        name: '更新通知',
        type: 'action',
        description: '更新通知信息'
      },
      {
        code: 'notifications:delete',
        name: '删除通知',
        type: 'action',
        description: '删除通知'
      }
    ]

    for (const op of notificationOperations) {
      const operation = await prisma.operation.upsert({
        where: { code: op.code },
        update: {},
        create: {
          code: op.code,
          name: op.name,
          type: op.type,
          description: op.description,
          enabled: true
        }
      })
      console.log(`通知操作 ${op.name} 创建成功:`, operation)
    }

    // 将资源和操作分配给管理员角色
    const adminRole = await prisma.role.findUnique({
      where: { code: 'ADMIN' }
    })

    if (adminRole) {
      // 获取所有操作
      const allOperations = await prisma.operation.findMany()
      const allResources = await prisma.resource.findMany()

      // 将所有资源和操作分配给管理员
      await prisma.role.update({
        where: { id: adminRole.id },
        data: {
          resources: {
            connect: allResources.map(resource => ({ id: resource.id }))
          },
          operations: {
            connect: allOperations.map(operation => ({ id: operation.id }))
          }
        }
      })
      console.log('资源和操作已分配给管理员角色')
    }

    console.log('资源和操作数据初始化完成')
  } catch (error) {
    console.error('初始化资源和操作数据失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
