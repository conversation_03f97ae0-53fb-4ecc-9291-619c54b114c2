[2025-04-27T02:29:00.880Z] 开始一次性导入所有历史数据...
[2025-04-27T02:29:00.881Z] 开始导入历史任务数据...
[2025-04-27T02:29:00.882Z] 从远程接口获取历史任务数据...
[2025-04-27T02:29:00.882Z] 获取到 3 条历史任务数据
[2025-04-27T02:29:00.950Z] 需要导入 3 条新历史任务数据
[2025-04-27T02:29:00.954Z] 导入历史任务数据失败: 
Invalid `prisma.user.findFirst()` invocation in
/Users/<USER>/Documents/5gweb_0402/scripts/import-historical-data.js:214:43

  211 let importedCount = 0;
  212 if (newTasks.length > 0) {
  213   // 获取默认管理员用户ID
→ 214   const adminUser = await prisma.user.findFirst({
          where: {
            role: "admin"
                  ~~~~~~~
          },
          select: {
            id: true
          }
        })

Argument `role`: Invalid value provided. Expected RoleRelationFilter or roleWhereInput, provided String.
[2025-04-27T02:29:00.954Z] 开始导入历史外呼详情数据...
[2025-04-27T02:29:00.955Z] 从远程接口获取历史外呼详情数据...
[2025-04-27T02:29:00.955Z] 获取到 4 条历史外呼详情数据
[2025-04-27T02:29:00.959Z] 需要导入 4 条新历史外呼详情数据
[2025-04-27T02:29:00.962Z] 导入历史外呼详情数据失败: 
Invalid `prisma.user.findFirst()` invocation in
/Users/<USER>/Documents/5gweb_0402/scripts/import-historical-data.js:290:43

  287 })).map(task => [task.externalId, task.id]));
  288 
  289 // 获取默认管理员用户ID
→ 290 const adminUser = await prisma.user.findFirst({
        where: {
          role: "admin"
                ~~~~~~~
        },
        select: {
          id: true
        }
      })

Argument `role`: Invalid value provided. Expected RoleRelationFilter or roleWhereInput, provided String.
[2025-04-27T02:29:00.962Z] 历史数据导入完成!
[2025-04-27T02:29:00.962Z] 导入任务数据: undefined/undefined 条
[2025-04-27T02:29:00.962Z] 导入外呼详情数据: undefined/undefined 条
[2025-04-27T02:29:00.963Z] 总耗时: 0.08 秒
[2025-04-27T02:29:38.310Z] 开始一次性导入所有历史数据...
[2025-04-27T02:29:38.311Z] 开始导入历史任务数据...
[2025-04-27T02:29:38.311Z] 从远程接口获取历史任务数据...
[2025-04-27T02:29:38.311Z] 获取到 3 条历史任务数据
[2025-04-27T02:29:38.376Z] 需要导入 3 条新历史任务数据
[2025-04-27T02:29:38.418Z] 导入历史任务数据失败: 
Invalid `prisma.task.createMany()` invocation in
/Users/<USER>/Documents/5gweb_0402/scripts/import-historical-data.js:246:27

  243 const batchSize = 100;
  244 for (let i = 0; i < tasksToImport.length; i += batchSize) {
  245   const batch = tasksToImport.slice(i, i + batchSize);
→ 246   await prisma.task.createMany(
Foreign key constraint violated: `task_userId_fkey (index)`
[2025-04-27T02:29:38.418Z] 开始导入历史外呼详情数据...
[2025-04-27T02:29:38.418Z] 从远程接口获取历史外呼详情数据...
[2025-04-27T02:29:38.418Z] 获取到 4 条历史外呼详情数据
[2025-04-27T02:29:38.421Z] 需要导入 4 条新历史外呼详情数据
[2025-04-27T02:29:38.423Z] 历史数据导入完成!
[2025-04-27T02:29:38.423Z] 导入任务数据: undefined/undefined 条
[2025-04-27T02:29:38.423Z] 导入外呼详情数据: 0/4 条
[2025-04-27T02:29:38.423Z] 总耗时: 0.11 秒
[2025-04-27T02:37:53.484Z] 开始一次性导入所有历史数据...
[2025-04-27T02:37:53.486Z] 开始导入历史任务数据...
[2025-04-27T02:37:53.486Z] 从远程接口获取历史任务数据...
[2025-04-27T02:37:53.486Z] 获取到 3 条历史任务数据
[2025-04-27T02:37:53.557Z] 需要导入 3 条新历史任务数据
[2025-04-27T02:37:53.590Z] 导入历史任务数据失败: 
Invalid `prisma.task.createMany()` invocation in
/Users/<USER>/Documents/5gweb_0402/scripts/import-historical-data.js:237:27

  234 const batchSize = 100;
  235 for (let i = 0; i < tasksToImport.length; i += batchSize) {
  236   const batch = tasksToImport.slice(i, i + batchSize);
→ 237   await prisma.task.createMany(
Foreign key constraint violated: `task_userId_fkey (index)`
[2025-04-27T02:37:53.591Z] 开始导入历史外呼详情数据...
[2025-04-27T02:37:53.591Z] 从远程接口获取历史外呼详情数据...
[2025-04-27T02:37:53.591Z] 获取到 4 条历史外呼详情数据
[2025-04-27T02:37:53.594Z] 需要导入 4 条新历史外呼详情数据
[2025-04-27T02:37:53.594Z] 历史数据导入完成!
[2025-04-27T02:37:53.595Z] 导入任务数据: undefined/undefined 条
[2025-04-27T02:37:53.595Z] 导入外呼详情数据: 0/4 条
[2025-04-27T02:37:53.595Z] 总耗时: 0.11 秒
[2025-04-27T02:38:57.516Z] 开始一次性导入所有历史数据...
[2025-04-27T02:38:57.517Z] 开始导入历史任务数据...
[2025-04-27T02:38:57.518Z] 从远程接口获取历史任务数据...
[2025-04-27T02:38:57.518Z] 获取到 3 条历史任务数据
[2025-04-27T02:38:57.586Z] 需要导入 3 条新历史任务数据
[2025-04-27T02:38:57.600Z] 导入历史任务数据失败: 
Invalid `prisma.task.createMany()` invocation in
/Users/<USER>/Documents/5gweb_0402/scripts/import-historical-data.js:237:27

  234 const batchSize = 100;
  235 for (let i = 0; i < tasksToImport.length; i += batchSize) {
  236   const batch = tasksToImport.slice(i, i + batchSize);
→ 237   await prisma.task.createMany(
Foreign key constraint violated: `task_userId_fkey (index)`
[2025-04-27T02:38:57.600Z] 开始导入历史外呼详情数据...
[2025-04-27T02:38:57.601Z] 从远程接口获取历史外呼详情数据...
[2025-04-27T02:38:57.601Z] 获取到 4 条历史外呼详情数据
[2025-04-27T02:38:57.605Z] 需要导入 4 条新历史外呼详情数据
[2025-04-27T02:38:57.606Z] 历史数据导入完成!
[2025-04-27T02:38:57.607Z] 导入任务数据: undefined/undefined 条
[2025-04-27T02:38:57.607Z] 导入外呼详情数据: 0/4 条
[2025-04-27T02:38:57.607Z] 总耗时: 0.09 秒
[2025-04-27T02:39:35.339Z] 开始一次性导入所有历史数据...
[2025-04-27T02:39:35.340Z] 开始导入历史任务数据...
[2025-04-27T02:39:35.340Z] 从远程接口获取历史任务数据...
[2025-04-27T02:39:35.340Z] 获取到 3 条历史任务数据
[2025-04-27T02:39:35.399Z] 需要导入 3 条新历史任务数据
[2025-04-27T02:39:35.413Z] 导入任务失败: 
Invalid `prisma.task.create()` invocation in
/Users/<USER>/Documents/5gweb_0402/scripts/import-historical-data.js:236:29

  233 // 逐个导入任务数据
  234 for (const taskData of tasksToImport) {
  235   try {
→ 236     await prisma.task.create(
Foreign key constraint violated: `task_userId_fkey (index)`
[2025-04-27T02:39:35.415Z] 导入任务失败: 
Invalid `prisma.task.create()` invocation in
/Users/<USER>/Documents/5gweb_0402/scripts/import-historical-data.js:236:29

  233 // 逐个导入任务数据
  234 for (const taskData of tasksToImport) {
  235   try {
→ 236     await prisma.task.create(
Foreign key constraint violated: `task_userId_fkey (index)`
[2025-04-27T02:39:35.418Z] 导入任务失败: 
Invalid `prisma.task.create()` invocation in
/Users/<USER>/Documents/5gweb_0402/scripts/import-historical-data.js:236:29

  233 // 逐个导入任务数据
  234 for (const taskData of tasksToImport) {
  235   try {
→ 236     await prisma.task.create(
Foreign key constraint violated: `task_userId_fkey (index)`
[2025-04-27T02:39:35.418Z] 开始导入历史外呼详情数据...
[2025-04-27T02:39:35.418Z] 从远程接口获取历史外呼详情数据...
[2025-04-27T02:39:35.418Z] 获取到 4 条历史外呼详情数据
[2025-04-27T02:39:35.421Z] 需要导入 4 条新历史外呼详情数据
[2025-04-27T02:39:35.427Z] 历史数据导入完成!
[2025-04-27T02:39:35.427Z] 导入任务数据: 0/3 条
[2025-04-27T02:39:35.427Z] 导入外呼详情数据: 0/4 条
[2025-04-27T02:39:35.427Z] 总耗时: 0.09 秒
