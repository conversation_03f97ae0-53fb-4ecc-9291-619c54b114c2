const ExcelJS = require('exceljs');
const path = require('path');
const fs = require('fs');

async function generateExcelTemplate() {
  try {
    // 创建一个新的工作簿
    const workbook = new ExcelJS.Workbook();
    
    // 添加一个工作表
    const worksheet = workbook.addWorksheet('任务导入');
    
    // 设置列头
    worksheet.columns = [
      { header: '电话号码', key: 'phone', width: 15 },
      { header: '姓名', key: 'name', width: 15 },
      { header: '备注', key: 'remark', width: 30 }
    ];
    
    // 添加示例数据
    worksheet.addRow({ phone: '13800138000', name: '张三', remark: 'VIP客户' });
    worksheet.addRow({ phone: '13900139000', name: '李四', remark: '普通客户' });
    worksheet.addRow({ phone: '13700137000', name: '王五', remark: '新客户' });
    
    // 设置表头样式
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };
    
    // 设置边框
    for (let i = 1; i <= 4; i++) {
      worksheet.getRow(i).eachCell({ includeEmpty: true }, cell => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
    }
    
    // 确保目录存在
    const templateDir = path.join(__dirname, '..', 'public', 'templates');
    if (!fs.existsSync(templateDir)) {
      fs.mkdirSync(templateDir, { recursive: true });
    }
    
    // 保存工作簿
    const filePath = path.join(templateDir, 'task-import-template.xlsx');
    await workbook.xlsx.writeFile(filePath);
    
    console.log(`Excel模板已生成: ${filePath}`);
  } catch (error) {
    console.error('生成Excel模板失败:', error);
  }
}

generateExcelTemplate();
