/**
 * 测试数据导入功能
 */

// 导入数据导入服务
// 注意：在 Next.js 项目中，我们需要使用绝对路径
const path = require('path');
const rootDir = path.resolve(__dirname, '..');
const { dataImportService } = require(path.join(rootDir, 'lib/services/data-import-service'));

// 测试获取历史任务数据
async function testGetHistoricalTasks() {
  try {
    console.log('测试获取历史任务数据...');

    // 导入视频外呼服务
    const { videoCallService } = require(path.join(rootDir, 'lib/api/video-call-service'));

    // 获取历史任务数据
    const historicalTasks = await videoCallService.getHistoricalTasks();

    console.log(`获取到 ${historicalTasks.length} 条历史任务数据`);

    if (historicalTasks.length > 0) {
      console.log('第一条历史任务数据:');
      console.log(JSON.stringify(historicalTasks[0], null, 2));
    }

    return historicalTasks;
  } catch (error) {
    console.error('获取历史任务数据失败:', error);
    return [];
  }
}

// 主函数
async function main() {
  console.log('开始测试数据导入功能...');

  try {
    // 测试获取历史任务数据
    const historicalTasks = await testGetHistoricalTasks();

    console.log(`共获取到 ${historicalTasks.length} 条历史任务数据`);

    // 如果成功获取到历史任务数据，则尝试导入
    if (historicalTasks.length > 0) {
      console.log('尝试导入历史数据...');

      // 导入所有历史数据
      const result = await dataImportService.importAllHistoricalData();

      if (result.success) {
        console.log('历史数据导入成功!');
        console.log(`导入任务数据: ${result.tasks.importedCount}/${result.tasks.totalCount} 条`);
        console.log(`导入外呼详情数据: ${result.callDetails.importedCount}/${result.callDetails.totalCount} 条`);
        console.log(`总耗时: ${result.duration}`);
      } else {
        console.error('历史数据导入失败:', result.message);
      }
    }
  } catch (error) {
    console.error('测试数据导入功能失败:', error);
  } finally {
    // 退出进程
    process.exit(0);
  }
}

// 执行主函数
main();
