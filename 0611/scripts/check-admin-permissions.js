const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // 查找管理员角色
    const adminRole = await prisma.role.findFirst({
      where: { code: 'ADMIN' },
      include: { menus: true }
    });
    
    if (adminRole) {
      console.log('管理员角色信息:');
      console.log(JSON.stringify({
        id: adminRole.id,
        code: adminRole.code,
        name: adminRole.name,
        type: adminRole.type,
        permissions: adminRole.permissions,
        menuCount: adminRole.menus.length
      }, null, 2));
      
      if (adminRole.menus.length > 0) {
        console.log(`\n关联的菜单 (${adminRole.menus.length}):`);
        adminRole.menus.forEach(menu => {
          console.log(`- ${menu.name} (${menu.path})`);
        });
      } else {
        console.log('\n没有关联的菜单');
      }
    } else {
      console.log('未找到管理员角色');
    }
    
    // 查找所有菜单
    const allMenus = await prisma.menu.findMany();
    console.log(`\n所有菜单 (${allMenus.length}):`);
    allMenus.forEach(menu => {
      console.log(`- ${menu.name} (${menu.path})`);
    });
    
    // 查找当前用户
    const user = await prisma.user.findFirst({
      where: { username: 'admin' },
      include: { role: true }
    });
    
    if (user) {
      console.log('\n管理员用户信息:');
      console.log(JSON.stringify({
        id: user.id,
        username: user.username,
        email: user.email,
        roleId: user.roleId,
        roleName: user.role?.name,
        roleCode: user.role?.code
      }, null, 2));
    } else {
      console.log('\n未找到管理员用户');
    }
    
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
