const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // 查找所有角色
    const roles = await prisma.role.findMany();
    
    console.log(`所有角色 (${roles.length}):`);
    roles.forEach(role => {
      console.log(`- ${role.name} (${role.code})`);
    });
    
    if (roles.length === 0) {
      console.log('没有找到任何角色，创建管理员角色...');
      
      // 创建管理员角色
      const adminRole = await prisma.role.create({
        data: {
          code: 'admin',
          name: '管理员',
          type: 'admin',
          permissions: ['*'] // 所有权限
        }
      });
      
      console.log('创建的管理员角色:', adminRole);
    }
    
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
