// 修复重复菜单项的脚本
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('开始修复重复菜单项...');
    
    // 1. 备份当前菜单数据
    const menus = await prisma.menu.findMany();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fs = require('fs');
    const path = require('path');
    const backupPath = path.join(__dirname, '..', `menu-backup-fix-${timestamp}.json`);
    
    fs.writeFileSync(backupPath, JSON.stringify(menus, null, 2));
    console.log(`菜单数据已备份到: ${backupPath}`);
    
    // 2. 查找重复的"网站设置"菜单项
    const settingsMenus = await prisma.menu.findMany({
      where: {
        name: '网站设置'
      }
    });
    
    console.log(`找到 ${settingsMenus.length} 个"网站设置"菜单项:`);
    settingsMenus.forEach(menu => {
      console.log(`- ID: ${menu.id}, 代码: ${menu.code}, 路径: ${menu.path}`);
    });
    
    // 3. 确定要保留的菜单项和要删除的菜单项
    const settingsMenu = settingsMenus.find(menu => menu.code === 'settings');
    const systemMenu = settingsMenus.find(menu => menu.code === 'system');
    
    if (!settingsMenu || !systemMenu) {
      console.log('未找到需要修复的菜单项');
      return;
    }
    
    console.log(`将保留菜单项: ID=${settingsMenu.id}, 代码=${settingsMenu.code}`);
    console.log(`将删除菜单项: ID=${systemMenu.id}, 代码=${systemMenu.code}`);
    
    // 4. 查找system菜单的子菜单
    const childMenus = await prisma.menu.findMany({
      where: {
        parentId: systemMenu.id
      }
    });
    
    console.log(`找到 ${childMenus.length} 个子菜单需要迁移:`);
    childMenus.forEach(menu => {
      console.log(`- ${menu.name} (${menu.code}): 路径=${menu.path}`);
    });
    
    // 5. 将子菜单的parentId更新为settingsMenu.id
    if (childMenus.length > 0) {
      console.log('开始迁移子菜单...');
      
      for (const childMenu of childMenus) {
        await prisma.menu.update({
          where: { id: childMenu.id },
          data: { parentId: settingsMenu.id }
        });
        console.log(`- 已将 "${childMenu.name}" 的父菜单更新为 "${settingsMenu.name}"`);
      }
    }
    
    // 6. 查找system菜单的角色关联
    const roleMenus = await prisma.$queryRaw`
      SELECT "A" as "roleId" FROM "_RoleMenus" WHERE "B" = ${systemMenu.id}
    `;
    
    console.log(`找到 ${roleMenus.length} 个角色关联需要迁移`);
    
    // 7. 为这些角色添加到settingsMenu的关联
    if (roleMenus.length > 0) {
      console.log('开始迁移角色关联...');
      
      for (const roleMenu of roleMenus) {
        // 检查是否已存在关联
        const existingRelation = await prisma.$queryRaw`
          SELECT * FROM "_RoleMenus" WHERE "A" = ${roleMenu.roleId} AND "B" = ${settingsMenu.id}
        `;
        
        if (existingRelation.length === 0) {
          await prisma.$executeRaw`
            INSERT INTO "_RoleMenus" ("A", "B") VALUES (${roleMenu.roleId}, ${settingsMenu.id})
          `;
          console.log(`- 已为角色ID=${roleMenu.roleId}添加到菜单"${settingsMenu.name}"的关联`);
        } else {
          console.log(`- 角色ID=${roleMenu.roleId}已关联到菜单"${settingsMenu.name}"`);
        }
      }
    }
    
    // 8. 删除system菜单
    await prisma.menu.delete({
      where: { id: systemMenu.id }
    });
    
    console.log(`已删除重复的菜单项: "${systemMenu.name}" (${systemMenu.code})`);
    console.log('菜单修复完成!');
    
  } catch (error) {
    console.error('修复菜单时出错:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => console.log('脚本执行完成'))
  .catch(e => {
    console.error('脚本执行失败:', e);
    process.exit(1);
  });
