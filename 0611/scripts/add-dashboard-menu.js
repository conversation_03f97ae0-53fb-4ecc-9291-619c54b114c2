const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // 创建仪表盘菜单
    const dashboardMenu = await prisma.menu.upsert({
      where: { code: 'dashboard' },
      update: {},
      create: {
        code: 'dashboard',
        name: '仪表盘',
        path: '/dashboard',
        icon: 'LayoutDashboard',
        order: 10,
        visible: true
      }
    });
    console.log('仪表盘菜单创建成功:', dashboardMenu.id);

    // 获取管理员角色
    const adminRole = await prisma.role.findFirst({
      where: { code: 'ADMIN' }
    });

    if (adminRole) {
      // 将菜单关联到管理员角色
      await prisma.role.update({
        where: { id: adminRole.id },
        data: {
          menus: {
            connect: [
              { id: dashboardMenu.id }
            ]
          }
        }
      });
      console.log('已将仪表盘菜单关联到管理员角色');
    } else {
      console.log('未找到管理员角色，无法关联菜单');
    }

    console.log('菜单数据添加完成！');
  } catch (error) {
    console.error('添加菜单数据失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
