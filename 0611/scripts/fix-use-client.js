/**
 * 修复"use client"指令位置的脚本
 * 
 * 此脚本用于将"use client"指令移动到文件顶部
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 要排除的目录
const EXCLUDED_DIRS = [
  'node_modules',
  '.next',
  'out',
  'public',
  'backups',
  'scripts'
];

// 要处理的文件扩展名
const FILE_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

/**
 * 修复文件中的"use client"指令位置
 * @param {string} filePath 文件路径
 * @returns {boolean} 是否修复了文件
 */
function fixUseClientDirective(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 检查文件是否包含"use client"指令
    if (!content.includes('"use client"')) {
      return false;
    }
    
    // 检查"use client"指令是否已经在文件顶部
    if (content.trim().startsWith('"use client"')) {
      return false;
    }
    
    // 提取"use client"指令前的内容
    const beforeUseClient = content.substring(0, content.indexOf('"use client"')).trim();
    
    // 提取"use client"指令后的内容
    const afterUseClient = content.substring(content.indexOf('"use client"') + '"use client"'.length).trim();
    
    // 重新组合内容，将"use client"指令放在文件顶部
    const newContent = `"use client"\n\n${beforeUseClient}\n\n${afterUseClient}`;
    
    // 写回文件
    fs.writeFileSync(filePath, newContent, 'utf8');
    
    console.log(`Fixed: ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
    return false;
  }
}

/**
 * 递归处理目录
 * @param {string} dir 目录路径
 * @returns {number} 处理的文件数量
 */
function processDirectory(dir) {
  let count = 0;
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    
    if (entry.isDirectory()) {
      if (!EXCLUDED_DIRS.includes(entry.name)) {
        count += processDirectory(fullPath);
      }
    } else if (entry.isFile() && FILE_EXTENSIONS.includes(path.extname(entry.name))) {
      if (fixUseClientDirective(fullPath)) {
        count++;
      }
    }
  }
  
  return count;
}

// 主函数
function main() {
  const rootDir = process.cwd();
  console.log(`Starting to fix "use client" directives in ${rootDir}`);
  
  const count = processDirectory(rootDir);
  
  console.log(`Fixed ${count} files.`);
}

main();
