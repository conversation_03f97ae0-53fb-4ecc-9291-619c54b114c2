/**
 * 高级日志替换脚本
 *
 * 此脚本用于将项目中的console.log语句替换为logger工具函数
 * 支持更复杂的模式匹配和替换
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 要排除的目录
const EXCLUDED_DIRS = [
  'node_modules',
  '.next',
  'out',
  'public',
  'backups',
  'scripts'
];

// 要处理的文件扩展名
const FILE_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// 导入检查
const IMPORT_STATEMENTS = [
  "import logger from '@/lib/utils/logger';",
  "import logger from '@/lib/utils/logger'",
  "import logger from \"@/lib/utils/logger\";"
];

/**
 * 检查文件是否已经导入了logger
 * @param {string} content 文件内容
 * @returns {boolean} 是否已导入
 */
function hasLoggerImport(content) {
  return IMPORT_STATEMENTS.some(stmt => content.includes(stmt));
}

/**
 * 添加logger导入语句
 * @param {string} content 文件内容
 * @returns {string} 更新后的内容
 */
function addLoggerImport(content) {
  // 如果已经有导入语句，在最后一个导入后添加
  const importRegex = /import .* from .*;\n/g;
  let lastImportMatch;
  let lastImportIndex = -1;

  while ((match = importRegex.exec(content)) !== null) {
    lastImportMatch = match;
    lastImportIndex = match.index + match[0].length;
  }

  if (lastImportIndex !== -1) {
    return content.slice(0, lastImportIndex) + IMPORT_STATEMENTS[0] + '\n' + content.slice(lastImportIndex);
  }

  // 如果没有导入语句，在文件开头添加
  return IMPORT_STATEMENTS[0] + '\n\n' + content;
}

/**
 * 替换文件中的console.log语句
 * @param {string} filePath 文件路径
 */
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let needsLoggerImport = false;

    // 替换console.log语句
    const consoleLogRegex = /console\.log\(([\s\S]*?)\);/g;
    let match;
    let replacements = [];

    while ((match = consoleLogRegex.exec(originalContent)) !== null) {
      const fullMatch = match[0];
      const args = match[1];
      const startPos = match.index;
      const endPos = startPos + fullMatch.length;

      // 根据内容确定日志级别
      let logLevel = 'debug';
      if (args.includes('错误') || args.includes('失败') || args.includes('异常')) {
        logLevel = 'error';
      } else if (args.includes('警告') || args.includes('注意')) {
        logLevel = 'warn';
      }

      const replacement = `logger.${logLevel}(${args});`;
      replacements.push({ startPos, endPos, replacement });
      needsLoggerImport = true;
    }

    // 替换console.error语句
    const consoleErrorRegex = /console\.error\(([\s\S]*?)\);/g;
    while ((match = consoleErrorRegex.exec(originalContent)) !== null) {
      const fullMatch = match[0];
      const args = match[1];
      const startPos = match.index;
      const endPos = startPos + fullMatch.length;

      const replacement = `logger.error(${args});`;
      replacements.push({ startPos, endPos, replacement });
      needsLoggerImport = true;
    }

    // 替换console.warn语句
    const consoleWarnRegex = /console\.warn\(([\s\S]*?)\);/g;
    while ((match = consoleWarnRegex.exec(originalContent)) !== null) {
      const fullMatch = match[0];
      const args = match[1];
      const startPos = match.index;
      const endPos = startPos + fullMatch.length;

      const replacement = `logger.warn(${args});`;
      replacements.push({ startPos, endPos, replacement });
      needsLoggerImport = true;
    }

    // 从后向前应用替换，避免位置偏移
    replacements.sort((a, b) => b.startPos - a.startPos);
    for (const { startPos, endPos, replacement } of replacements) {
      content = content.substring(0, startPos) + replacement + content.substring(endPos);
    }

    // 如果有替换且没有导入logger，添加导入
    if (needsLoggerImport && !hasLoggerImport(content)) {
      content = addLoggerImport(content);
    }

    // 如果内容有变化，写回文件
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Updated: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
    return false;
  }
}

/**
 * 递归处理目录
 * @param {string} dir 目录路径
 * @returns {number} 处理的文件数量
 */
function processDirectory(dir) {
  let count = 0;
  const entries = fs.readdirSync(dir, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);

    if (entry.isDirectory()) {
      if (!EXCLUDED_DIRS.includes(entry.name)) {
        count += processDirectory(fullPath);
      }
    } else if (entry.isFile() && FILE_EXTENSIONS.includes(path.extname(entry.name))) {
      if (processFile(fullPath)) {
        count++;
      }
    }
  }

  return count;
}

// 主函数
function main() {
  const rootDir = process.cwd();
  console.log(`Starting advanced replacement of console.log statements in ${rootDir}`);

  const count = processDirectory(rootDir);

  console.log(`Replacement completed. Updated ${count} files.`);
}

main();
