-- 插入示例任务数据
INSERT INTO "task" (
  "id", "name", "type", "content", "status", "progress",
  "importTime", "startTime", "completionTime", "creator",
  "userId", "externalId", "createdAt", "updatedAt"
) VALUES
(
  'task-001', '5G视频通知任务1', '5G视频通知', '通知内容1', '已完成', 100,
  '2023-01-01 07:00:00', '2023-01-01 08:00:00', '2023-01-01 10:00:00', '系统导入',
  'cm9frgxBu00015twlf657neu', 'ext-task-001', '2023-01-01 07:00:00', '2023-01-01 10:00:00'
),
(
  'task-002', '5G视频互动任务1', '5G视频互动', '互动内容1', '已完成', 100,
  '2023-01-02 07:00:00', '2023-01-02 08:00:00', '2023-01-02 10:00:00', '系统导入',
  'cm9frgxBu00015twlf657neu', 'ext-task-002', '2023-01-02 07:00:00', '2023-01-02 10:00:00'
),
(
  'task-003', '5G语音通话任务1', '5G语音通话', '通话内容1', '已完成', 100,
  '2023-01-03 07:00:00', '2023-01-03 08:00:00', '2023-01-03 10:00:00', '系统导入',
  'cm9frgxBu00015twlf657neu', 'ext-task-003', '2023-01-03 07:00:00', '2023-01-03 10:00:00'
);

-- 插入示例外呼详情数据
INSERT INTO "call_detail" (
  "id", "taskId", "taskName", "type", "content", "customerName",
  "phoneNumber", "connectionType", "startTime", "endTime", "duration",
  "ringTime", "intention", "externalCallId", "recordingUrl", "completionRate",
  "userId", "createdAt", "updatedAt"
) VALUES
(
  'call-001', 'task-001', '5G视频通知任务1', '5G视频通知', '通知内容1', '张三',
  '13800000001', '视频接通', '2023-01-01 08:10:00', '2023-01-01 08:15:00', '5分钟',
  '10秒', 'A', 'ext-call-001', 'https://example.com/recordings/001.mp4', 0.95,
  'cm9frgxBu00015twlf657neu', '2023-01-01 08:10:00', '2023-01-01 08:15:00'
),
(
  'call-002', 'task-001', '5G视频通知任务1', '5G视频通知', '通知内容1', '李四',
  '13800000002', '语音接通', '2023-01-01 08:20:00', '2023-01-01 08:25:00', '5分钟',
  '8秒', 'B', 'ext-call-002', 'https://example.com/recordings/002.mp4', 0.85,
  'cm9frgxBu00015twlf657neu', '2023-01-01 08:20:00', '2023-01-01 08:25:00'
),
(
  'call-003', 'task-002', '5G视频互动任务1', '5G视频互动', '互动内容1', '王五',
  '13800000003', '视频接通', '2023-01-02 08:10:00', '2023-01-02 08:20:00', '10分钟',
  '5秒', 'A', 'ext-call-003', 'https://example.com/recordings/003.mp4', 0.98,
  'cm9frgxBu00015twlf657neu', '2023-01-02 08:10:00', '2023-01-02 08:20:00'
),
(
  'call-004', 'task-003', '5G语音通话任务1', '5G语音通话', '通话内容1', '赵六',
  '13800000004', '语音接通', '2023-01-03 08:10:00', '2023-01-03 08:18:00', '8分钟',
  '3秒', 'C', 'ext-call-004', 'https://example.com/recordings/004.mp4', 0.75,
  'cm9frgxBu00015twlf657neu', '2023-01-03 08:10:00', '2023-01-03 08:18:00'
);
