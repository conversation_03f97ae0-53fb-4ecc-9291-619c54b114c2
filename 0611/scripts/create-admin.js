// 创建管理员用户的脚本
const { PrismaClient } = require('@prisma/client')
const { hash } = require('bcryptjs')
const prisma = new PrismaClient()

async function main() {
  try {
    // 获取管理员角色
    const adminRole = await prisma.role.findUnique({
      where: { code: 'ADMIN' }
    })

    if (!adminRole) {
      console.error('管理员角色不存在，请先运行 seed-roles.js')
      return
    }

    // 创建管理员用户
    const hashedPassword = await hash('Admin123456', 10)
    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        username: 'admin',
        email: '<EMAIL>',
        password: hashedPassword,
        name: '系统管理员',
        roleCode: adminRole.code,
        permissions: ['*'],
        status: 'active',
        emailVerified: new Date(),
        balance: 0,
        creditLimit: 0
      }
    })

    console.log('管理员用户创建成功:', admin)
  } catch (error) {
    console.error('创建管理员用户失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
