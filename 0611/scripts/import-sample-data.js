/**
 * 导入示例数据脚本
 * 使用 Prisma 导入示例任务和外呼详情数据
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('开始导入示例数据...');

    // 1. 获取有效的用户ID
    const user = await prisma.user.findFirst({
      where: {
        username: 'admin'
      }
    });

    if (!user) {
      throw new Error('找不到有效的用户');
    }

    console.log(`使用用户ID: ${user.id}`);

    // 2. 导入示例任务数据
    const task1 = await prisma.task.upsert({
      where: { id: 'task-001' },
      update: {},
      create: {
        id: 'task-001',
        name: '5G视频通知任务1',
        type: '5G视频通知',
        content: '通知内容1',
        status: '已完成',
        progress: 100,
        importTime: new Date('2023-01-01T07:00:00Z'),
        startTime: new Date('2023-01-01T08:00:00Z'),
        completionTime: new Date('2023-01-01T10:00:00Z'),
        creator: '系统导入',
        userId: user.id,
        externalId: 'ext-task-001',
        createdAt: new Date('2023-01-01T07:00:00Z'),
        updatedAt: new Date('2023-01-01T10:00:00Z')
      }
    });
    console.log(`导入任务1: ${task1.id}`);

    const task2 = await prisma.task.upsert({
      where: { id: 'task-002' },
      update: {},
      create: {
        id: 'task-002',
        name: '5G视频互动任务1',
        type: '5G视频互动',
        content: '互动内容1',
        status: '已完成',
        progress: 100,
        importTime: new Date('2023-01-02T07:00:00Z'),
        startTime: new Date('2023-01-02T08:00:00Z'),
        completionTime: new Date('2023-01-02T10:00:00Z'),
        creator: '系统导入',
        userId: user.id,
        externalId: 'ext-task-002',
        createdAt: new Date('2023-01-02T07:00:00Z'),
        updatedAt: new Date('2023-01-02T10:00:00Z')
      }
    });
    console.log(`导入任务2: ${task2.id}`);

    const task3 = await prisma.task.upsert({
      where: { id: 'task-003' },
      update: {},
      create: {
        id: 'task-003',
        name: '5G语音通话任务1',
        type: '5G语音通话',
        content: '通话内容1',
        status: '已完成',
        progress: 100,
        importTime: new Date('2023-01-03T07:00:00Z'),
        startTime: new Date('2023-01-03T08:00:00Z'),
        completionTime: new Date('2023-01-03T10:00:00Z'),
        creator: '系统导入',
        userId: user.id,
        externalId: 'ext-task-003',
        createdAt: new Date('2023-01-03T07:00:00Z'),
        updatedAt: new Date('2023-01-03T10:00:00Z')
      }
    });
    console.log(`导入任务3: ${task3.id}`);

    // 3. 导入示例外呼详情数据
    const callDetail1 = await prisma.call_detail.upsert({
      where: { id: 'call-001' },
      update: {},
      create: {
        id: 'call-001',
        taskId: task1.id,
        taskName: '5G视频通知任务1',
        type: '5G视频通知',
        content: '通知内容1',
        customerName: '张三',
        phoneNumber: '13800000001',
        connectionType: '视频接通',
        startTime: new Date('2023-01-01T08:10:00Z'),
        endTime: new Date('2023-01-01T08:15:00Z'),
        duration: '5分钟',
        ringTime: '10秒',
        intention: 'A',
        externalCallId: 'ext-call-001',
        recordingUrl: 'https://example.com/recordings/001.mp4',
        completionRate: 0.95,
        userId: user.id,
        createdAt: new Date('2023-01-01T08:10:00Z'),
        updatedAt: new Date('2023-01-01T08:15:00Z')
      }
    });
    console.log(`导入外呼详情1: ${callDetail1.id}`);

    const callDetail2 = await prisma.call_detail.upsert({
      where: { id: 'call-002' },
      update: {},
      create: {
        id: 'call-002',
        taskId: task1.id,
        taskName: '5G视频通知任务1',
        type: '5G视频通知',
        content: '通知内容1',
        customerName: '李四',
        phoneNumber: '13800000002',
        connectionType: '语音接通',
        startTime: new Date('2023-01-01T08:20:00Z'),
        endTime: new Date('2023-01-01T08:25:00Z'),
        duration: '5分钟',
        ringTime: '8秒',
        intention: 'B',
        externalCallId: 'ext-call-002',
        recordingUrl: 'https://example.com/recordings/002.mp4',
        completionRate: 0.85,
        userId: user.id,
        createdAt: new Date('2023-01-01T08:20:00Z'),
        updatedAt: new Date('2023-01-01T08:25:00Z')
      }
    });
    console.log(`导入外呼详情2: ${callDetail2.id}`);

    const callDetail3 = await prisma.call_detail.upsert({
      where: { id: 'call-003' },
      update: {},
      create: {
        id: 'call-003',
        taskId: task2.id,
        taskName: '5G视频互动任务1',
        type: '5G视频互动',
        content: '互动内容1',
        customerName: '王五',
        phoneNumber: '13800000003',
        connectionType: '视频接通',
        startTime: new Date('2023-01-02T08:10:00Z'),
        endTime: new Date('2023-01-02T08:20:00Z'),
        duration: '10分钟',
        ringTime: '5秒',
        intention: 'A',
        externalCallId: 'ext-call-003',
        recordingUrl: 'https://example.com/recordings/003.mp4',
        completionRate: 0.98,
        userId: user.id,
        createdAt: new Date('2023-01-02T08:10:00Z'),
        updatedAt: new Date('2023-01-02T08:20:00Z')
      }
    });
    console.log(`导入外呼详情3: ${callDetail3.id}`);

    const callDetail4 = await prisma.call_detail.upsert({
      where: { id: 'call-004' },
      update: {},
      create: {
        id: 'call-004',
        taskId: task3.id,
        taskName: '5G语音通话任务1',
        type: '5G语音通话',
        content: '通话内容1',
        customerName: '赵六',
        phoneNumber: '13800000004',
        connectionType: '语音接通',
        startTime: new Date('2023-01-03T08:10:00Z'),
        endTime: new Date('2023-01-03T08:18:00Z'),
        duration: '8分钟',
        ringTime: '3秒',
        intention: 'C',
        externalCallId: 'ext-call-004',
        recordingUrl: 'https://example.com/recordings/004.mp4',
        completionRate: 0.75,
        userId: user.id,
        createdAt: new Date('2023-01-03T08:10:00Z'),
        updatedAt: new Date('2023-01-03T08:18:00Z')
      }
    });
    console.log(`导入外呼详情4: ${callDetail4.id}`);

    console.log('示例数据导入完成!');
  } catch (error) {
    console.error('导入示例数据失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
