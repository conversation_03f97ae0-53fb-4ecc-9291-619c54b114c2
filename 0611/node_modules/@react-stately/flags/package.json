{"name": "@react-stately/flags", "version": "3.1.0", "description": "Spectrum UI components in React", "license": "Apache-2.0", "main": "dist/main.js", "module": "dist/module.js", "types": "dist/types.d.ts", "exports": {"types": "./dist/types.d.ts", "import": "./dist/import.mjs", "require": "./dist/main.js"}, "source": "src/index.ts", "files": ["dist", "src"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/adobe/react-spectrum"}, "dependencies": {"@swc/helpers": "^0.5.0"}, "publishConfig": {"access": "public"}, "gitHead": "4d3c72c94eea2d72eb3a0e7d56000c6ef7e39726"}