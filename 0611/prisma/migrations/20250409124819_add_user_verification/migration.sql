-- AlterTable
ALTER TABLE "user" ADD COLUMN     "address" TEXT,
ADD COLUMN     "city" TEXT,
ADD COLUMN     "district" TEXT,
ADD COLUMN     "phone" TEXT,
ADD COLUMN     "province" TEXT,
ADD COLUMN     "verificationStatus" TEXT DEFAULT 'none',
ADD COLUMN     "verificationType" TEXT DEFAULT 'none',
ADD COLUMN     "wechat" TEXT;

-- CreateTable
CREATE TABLE "user_verification" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "remark" TEXT,
    "reviewerId" TEXT,
    "reviewedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "realName" TEXT,
    "idCardNumber" TEXT,
    "idCardFront" TEXT,
    "idCardBack" TEXT,
    "idCardHolding" TEXT,
    "companyName" TEXT,
    "legalPerson" TEXT,
    "legalPersonIdCard" TEXT,
    "socialCreditCode" TEXT,
    "businessLicense" TEXT,
    "otherDocuments" TEXT[] DEFAULT ARRAY[]::TEXT[],

    CONSTRAINT "user_verification_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_verification_userId_key" ON "user_verification"("userId");

-- CreateIndex
CREATE INDEX "user_verification_userId_idx" ON "user_verification"("userId");

-- CreateIndex
CREATE INDEX "user_verification_status_idx" ON "user_verification"("status");

-- CreateIndex
CREATE INDEX "user_verification_type_idx" ON "user_verification"("type");

-- AddForeignKey
ALTER TABLE "user_verification" ADD CONSTRAINT "user_verification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
