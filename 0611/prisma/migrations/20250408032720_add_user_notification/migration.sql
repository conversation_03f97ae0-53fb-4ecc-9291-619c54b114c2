/*
  Warnings:

  - You are about to drop the column `expiryDate` on the `notification` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[userId,notificationId]` on the table `userNotification` will be added. If there are existing duplicate values, this will fail.
  - Made the column `publishedAt` on table `notification` required. This step will fail if there are existing NULL values in that column.

*/
-- DropIndex
DROP INDEX "userNotification_notificationId_idx";

-- DropIndex
DROP INDEX "userNotification_notificationId_userId_key";

-- DropIndex
DROP INDEX "userNotification_userId_idx";

-- AlterTable
ALTER TABLE "notification" DROP COLUMN "expiryDate",
ALTER COLUMN "status" SET DEFAULT 'published',
ALTER COLUMN "priority" DROP DEFAULT,
ALTER COLUMN "createdBy" SET DEFAULT 'system',
ALTER COLUMN "publishedAt" SET NOT NULL,
ALTER COLUMN "publishedAt" SET DEFAULT CURRENT_TIMESTAMP,
ALTER COLUMN "sendToAll" SET DEFAULT true;

-- CreateIndex
CREATE UNIQUE INDEX "userNotification_userId_notificationId_key" ON "userNotification"("userId", "notificationId");
