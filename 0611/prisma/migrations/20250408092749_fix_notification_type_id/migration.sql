/*
  Warnings:

  - You are about to drop the column `type` on the `notification` table. All the data in the column will be lost.
  - The primary key for the `notificationType` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `color` on the `notificationType` table. All the data in the column will be lost.
  - You are about to drop the column `enabled` on the `notificationType` table. All the data in the column will be lost.
  - You are about to drop the column `icon` on the `notificationType` table. All the data in the column will be lost.
  - You are about to drop the column `priority` on the `notificationType` table. All the data in the column will be lost.
  - The `id` column on the `notificationType` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `parentId` column on the `notificationType` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the `notification_types` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `notifications` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `typeId` to the `notification` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "notificationType" DROP CONSTRAINT "notificationType_parentId_fkey";

-- DropForeignKey
ALTER TABLE "notifications" DROP CONSTRAINT "notifications_typeId_fkey";

-- AlterTable
ALTER TABLE "notification" DROP COLUMN "type",
ADD COLUMN     "typeId" INTEGER NOT NULL,
ALTER COLUMN "priority" SET DEFAULT 'medium';

-- AlterTable
ALTER TABLE "notificationType" DROP CONSTRAINT "notificationType_pkey",
DROP COLUMN "color",
DROP COLUMN "enabled",
DROP COLUMN "icon",
DROP COLUMN "priority",
DROP COLUMN "id",
ADD COLUMN     "id" SERIAL NOT NULL,
DROP COLUMN "parentId",
ADD COLUMN     "parentId" INTEGER,
ADD CONSTRAINT "notificationType_pkey" PRIMARY KEY ("id");

-- DropTable
DROP TABLE "notification_types";

-- DropTable
DROP TABLE "notifications";

-- AddForeignKey
ALTER TABLE "notification" ADD CONSTRAINT "notification_typeId_fkey" FOREIGN KEY ("typeId") REFERENCES "notificationType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notificationType" ADD CONSTRAINT "notificationType_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "notificationType"("id") ON DELETE SET NULL ON UPDATE CASCADE;
