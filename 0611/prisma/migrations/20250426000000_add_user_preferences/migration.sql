-- 创建用户偏好设置表
-- 这个迁移只添加新表，不会修改任何现有表结构
CREATE TABLE "user_preferences" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "preferences" JSONB NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "user_preferences_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- 创建唯一索引，确保每个用户只有一条偏好记录
CREATE UNIQUE INDEX "user_preferences_userId_key" ON "user_preferences"("userId");

-- 创建普通索引，提高查询性能
CREATE INDEX "user_preferences_userId_idx" ON "user_preferences"("userId");
