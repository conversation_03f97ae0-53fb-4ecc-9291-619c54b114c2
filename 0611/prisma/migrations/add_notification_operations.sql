-- 添加通知相关的操作权限
INSERT INTO operation (id, code, name, type, enabled, "menuId", "createdAt", "updatedAt")
VALUES 
  ('op_notification_list', 'notifications:list', '查看通知列表', 'action', true, (SELECT id FROM menu WHERE code = 'notification'), NOW(), NOW()),
  ('op_notification_create', 'notifications:create', '创建通知', 'action', true, (SELECT id FROM menu WHERE code = 'notification'), NOW(), NOW()),
  ('op_notification_edit', 'notifications:edit', '编辑通知', 'action', true, (SELECT id FROM menu WHERE code = 'notification'), NOW(), NOW()),
  ('op_notification_delete', 'notifications:delete', '删除通知', 'action', true, (SELECT id FROM menu WHERE code = 'notification'), NOW(), NOW())
ON CONFLICT (code) DO NOTHING;

-- 为管理员角色添加所有通知操作权限
INSERT INTO "_RoleOperations" ("B", "A")
SELECT r.id, o.id 
FROM role r 
CROSS JOIN operation o 
WHERE r.code = 'admin' 
AND o.code LIKE 'notifications:%'
ON CONFLICT DO NOTHING;

-- 为普通用户添加查看通知列表的权限
INSERT INTO "_RoleOperations" ("B", "A")
SELECT r.id, o.id 
FROM role r 
CROSS JOIN operation o 
WHERE r.code = 'user' 
AND o.code = 'notifications:list'
ON CONFLICT DO NOTHING; 