-- 添加费率管理菜单
INSERT INTO "menu" ("id", "code", "name", "path", "icon", "parentId", "order", "visible", "createdAt", "updatedAt")
VALUES (
  gen_random_uuid(),
  'rates',
  '费率管理',
  '/rates',
  'CreditCard',
  NULL,
  50,
  true,
  NOW(),
  NOW()
);

-- 添加费率资源
INSERT INTO "resource" ("id", "code", "name", "type", "description", "createdAt", "updatedAt")
VALUES (
  gen_random_uuid(),
  'rates',
  '费率管理',
  'menu',
  '费率管理资源',
  NOW(),
  NOW()
);

-- 添加费率操作
INSERT INTO "operation" ("id", "code", "name", "type", "description", "enabled", "createdAt", "updatedAt")
VALUES (
  gen_random_uuid(),
  'rates:manage',
  '管理费率',
  'action',
  '管理费率操作',
  true,
  NOW(),
  NOW()
);

-- 将费率菜单添加到管理员角色
INSERT INTO "_RoleMenus" ("A", "B")
SELECT r.id, m.id
FROM "role" r, "menu" m
WHERE r.code = 'ADMIN' AND m.code = 'rates'
ON CONFLICT DO NOTHING;

-- 将费率资源添加到管理员角色
INSERT INTO "_RoleResources" ("A", "B")
SELECT r.id, res.id
FROM "role" r, "resource" res
WHERE r.code = 'ADMIN' AND res.code = 'rates'
ON CONFLICT DO NOTHING;

-- 将费率操作添加到管理员角色
INSERT INTO "_RoleOperations" ("A", "B")
SELECT r.id, o.id
FROM "role" r, "operation" o
WHERE r.code = 'ADMIN' AND o.code = 'rates:manage'
ON CONFLICT DO NOTHING;

-- 更新管理员角色权限
UPDATE "role"
SET "permissions" = array_append("permissions", 'rates:manage')
WHERE "code" = 'ADMIN';
