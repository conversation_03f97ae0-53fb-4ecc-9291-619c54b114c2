-- 更新系统设置菜单为网站设置
UPDATE "menu"
SET "name" = '网站设置'
WHERE "code" = 'system' OR "name" = '系统设置';

-- 创建客户管理父菜单
INSERT INTO "menu" ("id", "code", "name", "path", "icon", "parentId", "order", "visible", "createdAt", "updatedAt")
VALUES (
  gen_random_uuid(),
  'customer-management',
  '客户管理',
  '/customer-management',
  'TeamOutlined',
  NULL,
  30,
  true,
  NOW(),
  NOW()
)
ON CONFLICT (code) DO UPDATE
SET "name" = '客户管理',
    "path" = '/customer-management',
    "icon" = 'TeamOutlined',
    "order" = 30,
    "visible" = true,
    "updatedAt" = NOW();

-- 获取客户管理父菜单ID并更新子菜单

-- 更新客户账户菜单，设置父菜单为客户管理
UPDATE "menu"
SET "name" = '客户账户',
    "parentId" = (SELECT id FROM "menu" WHERE "code" = 'customer-management'),
    "order" = 31,
    "updatedAt" = NOW()
WHERE "code" = 'customers';

-- 更新费率管理菜单，设置父菜单为客户管理
UPDATE "menu"
SET "parentId" = (SELECT id FROM "menu" WHERE "code" = 'customer-management'),
    "order" = 32,
    "updatedAt" = NOW()
WHERE "code" = 'rates';

-- 确保菜单设置菜单存在
INSERT INTO "menu" ("id", "code", "name", "path", "icon", "parentId", "order", "visible", "createdAt", "updatedAt")
SELECT
  gen_random_uuid(),
  'menu-settings',
  '菜单设置',
  '/settings?tab=menu',
  'menu',
  (SELECT id FROM "menu" WHERE "code" = 'system' OR "name" = '网站设置' LIMIT 1),
  160,
  true,
  NOW(),
  NOW()
WHERE NOT EXISTS (
  SELECT 1 FROM "menu" WHERE "code" = 'menu-settings'
);
