import logger from '@/lib/utils/logger';

import { PrismaClient } from '@prisma/client'
import * as bcrypt from 'bcryptjs'
import { hash } from 'bcryptjs'
import { seedOperations } from './seed/operations'

/**
 * Prisma 客户端实例
 */
const prisma = new PrismaClient()

/**
 * 角色接口定义
 * 用于定义系统角色结构
 */
interface Role {
  code: string      // 角色代码
  name: string      // 角色名称
  type: string      // 角色类型
  description: string // 角色描述
  permissions: string[] // 角色权限列表
}

/**
 * 策略接口定义
 * 用于定义系统权限策略结构
 */
interface Policy {
  name: string      // 策略名称
  description: string // 策略描述
  effect: string    // 策略效果
  priority: number  // 策略优先级
  enabled: boolean  // 是否启用
  resource: string  // 资源标识
  action: string    // 操作类型
  conditions?: string // 条件表达式
  code: string      // 策略代码
}

/**
 * 系统角色配置
 * 定义系统默认角色及其权限
 */
const ROLES: Record<string, Role> = {
  ADMIN: {
    code: "ADMIN",
    name: "管理员",
    type: "system",
    description: "系统管理员，拥有所有权限",
    permissions: [
      "USER_MANAGE",
      "ROLE_MANAGE",
      "PERMISSION_MANAGE",
      "SETTINGS_MANAGE",
    ],
  },
  USER: {
    code: "USER",
    name: "普通用户",
    type: "user",
    description: "普通用户，拥有基本权限",
    permissions: [
      "PROFILE_VIEW",
      "PROFILE_UPDATE",
      "TASK_VIEW",
      "TASK_CREATE",
      "TASK_UPDATE",
    ],
  },
}

/**
 * 默认策略配置
 * 定义系统默认的权限策略
 */
const DEFAULT_POLICIES: Policy[] = [
  {
    name: "admin-full-access",
    description: "管理员完全访问权限",
    effect: "allow",
    priority: 100,
    enabled: true,
    resource: "*",
    action: "*",
    conditions: undefined,
    code: "ADMIN_FULL_ACCESS",
  },
  {
    name: "user-basic-access",
    description: "用户基本访问权限",
    effect: "allow",
    priority: 50,
    enabled: true,
    resource: "profile",
    action: "view,update",
    conditions: undefined,
    code: "USER_BASIC_ACCESS",
  },
]

/**
 * 数据库初始化主函数
 * 用于创建系统默认的角色、策略和用户
 *
 * 处理流程：
 * 1. 创建系统角色
 * 2. 创建默认策略
 * 3. 创建管理员用户
 * 4. 创建测试用户
 *
 * 错误处理：
 * - 捕获并记录所有错误
 * - 确保数据库连接正确关闭
 */
async function main() {
  logger.debug('开始初始化数据...')

  // Create notification types
  const systemType = await prisma.notificationType.create({
    data: {
      code: 'SYSTEM',
      name: '系统通知',
      description: '系统相关的通知'
    }
  });

  const featureType = await prisma.notificationType.create({
    data: {
      code: 'FEATURE',
      name: '功能更新',
      description: '新功能发布通知'
    }
  });

  const securityType = await prisma.notificationType.create({
    data: {
      code: 'SECURITY',
      name: '安全提醒',
      description: '安全相关的通知'
    }
  });

  const taskType = await prisma.notificationType.create({
    data: {
      code: 'TASK',
      name: '任务通知',
      description: '任务相关的通知'
    }
  });

  // Create roles
  const adminRole = await prisma.role.upsert({
    where: { code: 'ADMIN' },
    update: {
      permissions: [
        '*',
        'role:view',
        'role:create',
        'role:update',
        'role:delete',
        'ROLE_MANAGE',
        'ROLE_CREATE',
        'ROLE_VIEW',
        'ROLE_UPDATE',
        'ROLE_DELETE'
      ]
    },
    create: {
      code: 'ADMIN',
      name: '管理员',
      type: 'system',
      description: '系统管理员',
      permissions: [
        '*',
        'role:view',
        'role:create',
        'role:update',
        'role:delete',
        'ROLE_MANAGE',
        'ROLE_CREATE',
        'ROLE_VIEW',
        'ROLE_UPDATE',
        'ROLE_DELETE'
      ],
      notificationEnabled: true
    }
  });

  const userRole = await prisma.role.upsert({
    where: { code: 'USER' },
    update: {},
    create: {
      code: 'USER',
      name: '普通用户',
      type: 'system',
      description: '普通用户，拥有基本权限',
      permissions: ['notifications:read']
    }
  });

  const editorRole = await prisma.role.upsert({
    where: { code: 'EDITOR' },
    update: {},
    create: {
      code: 'EDITOR',
      name: '编辑',
      type: 'system',
      description: '内容编辑，可以管理通知',
      permissions: ['notifications:read', 'notifications:create', 'notifications:edit', 'notifications:delete']
    }
  });

  // Create admin user
  const hashedPassword = await hash('admin123', 12)
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {
      roleCode: 'ADMIN',
      permissions: [
        '*',
        'role:view',
        'role:create',
        'role:update',
        'role:delete',
        'ROLE_MANAGE',
        'ROLE_CREATE',
        'ROLE_VIEW',
        'ROLE_UPDATE',
        'ROLE_DELETE'
      ]
    },
    create: {
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      roleCode: 'ADMIN',
      name: '系统管理员',
      permissions: [
        '*',
        'role:view',
        'role:create',
        'role:update',
        'role:delete',
        'ROLE_MANAGE',
        'ROLE_CREATE',
        'ROLE_VIEW',
        'ROLE_UPDATE',
        'ROLE_DELETE'
      ]
    }
  });

  const normalUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'user',
      email: '<EMAIL>',
      password: await bcrypt.hash('user123', 10),
      roleCode: userRole.code,
      permissions: ['notifications:read'],
      name: '普通用户'
    }
  });

  const editorUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'editor',
      email: '<EMAIL>',
      password: await bcrypt.hash('editor123', 10),
      roleCode: editorRole.code,
      permissions: ['notifications:read', 'notifications:create', 'notifications:edit', 'notifications:delete'],
      name: '内容编辑'
    }
  });

  // Create example notifications
  const systemNotification = await prisma.notification.create({
    data: {
      title: '系统维护通知',
      content: '系统将于本周六凌晨2点进行例行维护，预计持续2小时。',
      type: { connect: { id: systemType.id } },
      priority: 'high',
      status: 'published',
      createdBy: 'system'
    }
  });

  const featureNotification = await prisma.notification.create({
    data: {
      title: '新功能上线',
      content: '我们很高兴地通知您，新的通知管理功能已经上线！',
      type: { connect: { id: featureType.id } },
      priority: 'medium',
      status: 'published',
      createdBy: adminUser.id
    }
  });

  logger.debug('Seed data created:', {
    notificationTypes: [systemType, featureType, securityType, taskType],
    roles: [adminRole, userRole, editorRole],
    users: [adminUser, normalUser, editorUser],
    notifications: [systemNotification, featureNotification]
  });

  // Create main menus
  const dashboardMenu = await prisma.menu.upsert({
    where: { code: 'dashboard' },
    update: {
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
    create: {
      code: 'dashboard',
      name: '仪表盘',
      path: '/dashboard',
      icon: 'DashboardOutlined',
      order: 1,
      visible: true,
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
  })

  const taskMenu = await prisma.menu.upsert({
    where: { code: 'task' },
    update: {
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
    create: {
      code: 'task',
      name: '任务管理',
      path: '/task',
      icon: 'ProjectOutlined',
      order: 2,
      visible: true,
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
  })

  const notificationMenu = await prisma.menu.upsert({
    where: { code: 'notification' },
    update: {
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
    create: {
      code: 'notification',
      name: '通知中心',
      path: '/notification',
      icon: 'BellOutlined',
      order: 3,
      visible: true,
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
  })

  const userCenterMenu = await prisma.menu.upsert({
    where: { code: 'user-center' },
    update: {
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
    create: {
      code: 'user-center',
      name: '用户中心',
      path: '/user-center',
      icon: 'UserOutlined',
      order: 4,
      visible: true,
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
  })

  const accountMenu = await prisma.menu.upsert({
    where: { code: 'account' },
    update: {
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
    create: {
      code: 'account',
      name: '账户管理',
      path: '/account',
      icon: 'TeamOutlined',
      order: 5,
      visible: true,
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
  })

  const systemMenu = await prisma.menu.upsert({
    where: { code: 'system' },
    update: {
      roles: {
        connect: [{ code: adminRole.code }],
      },
    },
    create: {
      code: 'system',
      name: '系统管理',
      path: '/system',
      icon: 'SettingOutlined',
      order: 6,
      visible: true,
      roles: {
        connect: [{ code: adminRole.code }],
      },
    },
  })

  // Create submenus for task management
  const taskUploadMenu = await prisma.menu.upsert({
    where: { code: 'task-upload' },
    update: {
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
    create: {
      code: 'task-upload',
      name: '任务上传',
      path: '/task/upload',
      parentId: taskMenu.id,
      order: 1,
      visible: true,
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
  })

  const taskDetailMenu = await prisma.menu.upsert({
    where: { code: 'task-detail' },
    update: {
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
    create: {
      code: 'task-detail',
      name: '外呼详情',
      path: '/task/detail',
      parentId: taskMenu.id,
      order: 2,
      visible: true,
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
  })

  // Create submenus for account management
  const customerAccountMenu = await prisma.menu.upsert({
    where: { code: 'customer-account' },
    update: {
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
    create: {
      code: 'customer-account',
      name: '用户管理',
      path: '/account/customer',
      parentId: accountMenu.id,
      order: 1,
      visible: true,
      roles: {
        connect: [{ code: adminRole.code }, { code: userRole.code }],
      },
    },
  })

  // 初始化操作数据
  await seedOperations(prisma)

  console.log('初始化数据完成！')
}

// 执行数据库初始化
main()
  .catch((e) => {
    console.error('初始化数据失败:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })