-- 清理现有数据
TRUNCATE TABLE "menu" CASCADE;
TRUNCATE TABLE "role" CASCADE;
TRUNCATE TABLE "operation" CASCADE;
TRUNCATE TABLE "resource" CASCADE;
TRUNCATE TABLE "user" CASCADE;

-- 创建基础菜单
INSERT INTO "menu" (id, code, name, path, icon, "order", visible, "createdAt", "updatedAt")
VALUES 
  ('menu_dashboard', 'dashboard', '仪表盘', '/dashboard', 'DashboardOutlined', 1, true, NOW(), NOW()),
  ('menu_tasks', 'tasks', '任务管理', '/tasks', 'ProjectOutlined', 2, true, NOW(), NOW()),
  ('menu_notifications', 'notifications', '通知中心', '/notifications', 'BellOutlined', 3, true, NOW(), NOW()),
  ('menu_users', 'users', '用户管理', '/users', 'UserOutlined', 4, true, NOW(), NOW()),
  ('menu_roles', 'roles', '角色管理', '/roles', 'TeamOutlined', 5, true, NOW(), NOW()),
  ('menu_settings', 'settings', '系统设置', '/settings', 'SettingOutlined', 6, true, NOW(), NOW());

-- 创建角色
INSERT INTO "role" (id, code, name, type, description, permissions, "createdAt", "updatedAt")
VALUES 
  ('role_001', 'admin', '管理员', 'system', '系统管理员', ARRAY['*']::text[], NOW(), NOW()),
  ('role_002', 'user', '普通用户', 'customer', '普通用户', ARRAY['read', 'notifications:list']::text[], NOW(), NOW()),
  ('role_003', 'manager', '部门经理', 'system', '部门管理员', ARRAY['read', 'write', 'manage']::text[], NOW(), NOW());

-- 创建测试用户账号
INSERT INTO "user" (id, username, email, password, "roleCode", "createdAt", "updatedAt", permissions)
VALUES
  ('user_001', 'admin', '<EMAIL>', '$2a$10$3u07Tp4vWApTdOBaz34Z5OQxl0.hdCBFjTuvOEaJ.crAJ5UrZ4k9m', 'admin', NOW(), NOW(), ARRAY[]::text[]),
  ('user_002', 'user', '<EMAIL>', '$2a$10$3u07Tp4vWApTdOBaz34Z5OQxl0.hdCBFjTuvOEaJ.crAJ5UrZ4k9m', 'user', NOW(), NOW(), ARRAY[]::text[]),
  ('user_003', 'manager', '<EMAIL>', '$2a$10$3u07Tp4vWApTdOBaz34Z5OQxl0.hdCBFjTuvOEaJ.crAJ5UrZ4k9m', 'manager', NOW(), NOW(), ARRAY[]::text[]);

-- 创建操作权限
INSERT INTO "operation" (id, code, name, type, description, "menuId", enabled, "createdAt", "updatedAt")
VALUES 
  -- 通知管理操作
  ('op_021', 'notifications:list', '查看通知列表', 'action', '查看通知列表权限', 'menu_notifications', true, NOW(), NOW()),
  ('op_022', 'notifications:create', '创建通知', 'action', '创建通知权限', 'menu_notifications', true, NOW(), NOW()),
  ('op_023', 'notifications:edit', '编辑通知', 'action', '编辑通知权限', 'menu_notifications', true, NOW(), NOW()),
  ('op_024', 'notifications:delete', '删除通知', 'action', '删除通知权限', 'menu_notifications', true, NOW(), NOW()),
  ('op_025', 'notifications:markAsRead', '标记通知已读', 'action', '标记通知为已读权限', 'menu_notifications', true, NOW(), NOW());

-- 创建资源
INSERT INTO "resource" (id, code, name, type, description, "createdAt", "updatedAt")
VALUES 
  ('res_006', 'notifications', '通知管理', 'module', '通知管理模块', NOW(), NOW());

-- 关联角色与菜单
INSERT INTO "_RoleMenus" ("A", "B")
SELECT m.id, r.id
FROM "menu" m, "role" r
WHERE r.code = 'admin';

INSERT INTO "_RoleMenus" ("A", "B")
SELECT m.id, r.id
FROM "menu" m, "role" r
WHERE r.code = 'user'
AND m.code IN ('dashboard', 'tasks', 'notifications');

INSERT INTO "_RoleMenus" ("A", "B")
SELECT m.id, r.id
FROM "menu" m, "role" r
WHERE r.code = 'manager'
AND m.code IN ('dashboard', 'tasks', 'notifications', 'users');

-- 关联角色与操作权限
INSERT INTO "_RoleOperations" ("A", "B")
SELECT o.id, r.id
FROM "operation" o, "role" r
WHERE r.code = 'admin';

INSERT INTO "_RoleOperations" ("A", "B")
SELECT o.id, r.id
FROM "operation" o, "role" r
WHERE r.code = 'user'
AND o.code IN ('notifications:list', 'notifications:markAsRead');

INSERT INTO "_RoleOperations" ("A", "B")
SELECT o.id, r.id
FROM "operation" o, "role" r
WHERE r.code = 'manager'
AND o.code IN ('notifications:list', 'notifications:create', 'notifications:edit', 'notifications:markAsRead');

-- 关联角色与资源
INSERT INTO "_RoleResources" ("A", "B")
SELECT res.id, r.id
FROM "resource" res, "role" r
WHERE r.code = 'admin';

INSERT INTO "_RoleResources" ("A", "B")
SELECT res.id, r.id
FROM "resource" res, "role" r
WHERE r.code = 'user'
AND res.code IN ('notifications');

INSERT INTO "_RoleResources" ("A", "B")
SELECT res.id, r.id
FROM "resource" res, "role" r
WHERE r.code = 'manager'
AND res.code IN ('notifications');

-- 关联资源与操作权限
INSERT INTO "_ResourceOperations" ("A", "B")
VALUES 
  ('op_021', 'res_006'), -- 查看通知列表
  ('op_022', 'res_006'), -- 创建通知
  ('op_023', 'res_006'), -- 编辑通知
  ('op_024', 'res_006'), -- 删除通知
  ('op_025', 'res_006'); -- 标记已读