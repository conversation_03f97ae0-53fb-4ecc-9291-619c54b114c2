-- 清理现有的权限关联
DELETE FROM "_RoleMenus";
DELETE FROM "_RoleOperations";

-- 为管理员角色关联所有菜单
INSERT INTO "_RoleMenus" ("A", "B")
SELECT m.id, r.id
FROM menu m, role r
WHERE r.code = 'ADMIN';

-- 为普通用户关联基础菜单
INSERT INTO "_RoleMenus" ("A", "B")
SELECT m.id, r.id
FROM menu m, role r
WHERE r.code = 'USER'
AND m.code IN ('dashboard', 'tasks', 'tasks.upload', 'tasks.details', 'notifications', 'user.profile');

-- 为管理员角色关联所有操作权限
INSERT INTO "_RoleOperations" ("A", "B")
SELECT o.id, r.id
FROM operation o, role r
WHERE r.code = 'ADMIN';

-- 为普通用户关联基础操作权限
INSERT INTO "_RoleOperations" ("A", "B")
SELECT o.id, r.id
FROM operation o, role r
WHERE r.code = 'USER'
AND o.code IN (
  'dashboard.view',
  'tasks.upload',
  'tasks.view',
  'tasks.details',
  'notification.view',
  'notification.read',
  'user.profile.view',
  'user.profile.edit'
);

-- 插入角色
INSERT INTO role ("id", "code", "name", "description", "createdAt", "updatedAt")
VALUES 
  ('role_001', 'admin', '管理员', '系统管理员', NOW(), NOW()),
  ('role_002', 'user', '普通用户', '普通用户', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  code = EXCLUDED.code,
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  "updatedAt" = NOW();

-- 关联角色与操作权限
INSERT INTO "_OperationToRole" ("A", "B")
VALUES
  -- 管理员角色权限
  ('op_001', 'role_001'), -- 查看用户列表
  ('op_002', 'role_001'), -- 创建用户
  ('op_003', 'role_001'), -- 更新用户
  ('op_004', 'role_001'), -- 删除用户
  ('op_005', 'role_001'), -- 查看角色列表
  ('op_006', 'role_001'), -- 创建角色
  ('op_007', 'role_001'), -- 更新角色
  ('op_008', 'role_001'), -- 删除角色
  ('op_009', 'role_001'), -- 查看菜单列表
  ('op_010', 'role_001'), -- 创建菜单
  ('op_011', 'role_001'), -- 更新菜单
  ('op_012', 'role_001'), -- 删除菜单
  ('op_013', 'role_001'), -- 查看操作权限列表
  ('op_014', 'role_001'), -- 创建操作权限
  ('op_015', 'role_001'), -- 更新操作权限
  ('op_016', 'role_001'), -- 删除操作权限
  ('op_017', 'role_001'), -- 查看资源列表
  ('op_018', 'role_001'), -- 创建资源
  ('op_019', 'role_001'), -- 更新资源
  ('op_020', 'role_001'), -- 删除资源
  ('op_021', 'role_001'), -- 查看通知列表
  ('op_022', 'role_001'), -- 创建通知
  ('op_023', 'role_001'), -- 编辑通知
  ('op_024', 'role_001'), -- 删除通知

  -- 普通用户角色权限
  ('op_001', 'role_002'), -- 查看用户列表
  ('op_021', 'role_002'),  -- 查看通知列表

  -- 管理员角色权限
  ('role_001', 'op_001'), -- notifications:list
  ('role_001', 'op_002'), -- notifications:create
  ('role_001', 'op_003'), -- notifications:edit
  ('role_001', 'op_004'), -- notifications:delete
  ('role_001', 'op_005'), -- notifications:publish

  -- 管理员角色权限
  ('role_003', 'op_001'), -- notifications:list
  ('role_003', 'op_002'); -- notifications:create

ON CONFLICT ("A", "B") DO NOTHING;