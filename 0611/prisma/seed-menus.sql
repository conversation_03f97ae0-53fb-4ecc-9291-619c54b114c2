-- 插入基础菜单项
INSERT INTO "Menu" (id, code, name, path, icon, "parentId", "order", visible, "createdAt", "updatedAt")
VALUES
  ('menu_dashboard', 'dashboard', '仪表盘', '/dashboard', 'LayoutDashboard', NULL, 1, true, NOW(), NOW()),
  
  ('menu_tasks', 'tasks', '任务管理', '/tasks', 'Upload', NULL, 2, true, NOW(), NOW()),
  ('menu_tasks_upload', 'tasks.upload', '任务上传', '/tasks', NULL, 'menu_tasks', 1, true, NOW(), NOW()),
  ('menu_tasks_details', 'tasks.details', '外呼详情', '/tasks/details', NULL, 'menu_tasks', 2, true, NOW(), NOW()),
  
  ('menu_notifications', 'notifications', '通知中心', '/notifications', 'Bell', NULL, 3, true, NOW(), NOW()),
  ('menu_user', 'user.profile', '用户中心', '/user', 'User', NULL, 4, true, NOW(), NOW()),
  
  ('menu_accounts', 'accounts', '账户管理', '/accounts', 'Users', NULL, 5, true, NOW(), NOW()),
  ('menu_accounts_customer', 'accounts.customer', '客户账户', '/accounts/customer', NULL, 'menu_accounts', 1, true, NOW(), NOW()),
  ('menu_accounts_admin', 'accounts.admin', '管理员账户', '/accounts/admin', NULL, 'menu_accounts', 2, true, NOW(), NOW()),
  
  ('menu_settings', 'settings', '系统设置', '/settings', 'Settings', NULL, 6, true, NOW(), NOW()),
  
  -- 系统管理
  ('menu_system', 'system', '系统管理', '/system', 'Settings', NULL, 1, true, NOW(), NOW()),
  
  -- 用户管理
  ('menu_users', 'users', '用户管理', '/system/users', 'Users', 'menu_system', 1, true, NOW(), NOW()),
  
  -- 角色管理
  ('menu_roles', 'roles', '角色管理', '/system/roles', 'Shield', 'menu_system', 2, true, NOW(), NOW()),
  
  -- 菜单管理
  ('menu_menus', 'menus', '菜单管理', '/system/menus', 'Menu', 'menu_system', 3, true, NOW(), NOW()),
  
  -- 操作权限管理
  ('menu_operations', 'operations', '操作权限', '/system/operations', 'Key', 'menu_system', 4, true, NOW(), NOW()),
  
  -- 资源管理
  ('menu_resources', 'resources', '资源管理', '/system/resources', 'Database', 'menu_system', 5, true, NOW(), NOW())
ON CONFLICT (code) DO UPDATE SET
  name = EXCLUDED.name,
  path = EXCLUDED.path,
  icon = EXCLUDED.icon,
  "parentId" = EXCLUDED."parentId",
  "order" = EXCLUDED."order",
  visible = EXCLUDED.visible,
  "updatedAt" = NOW(); 