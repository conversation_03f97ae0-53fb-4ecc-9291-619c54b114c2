/**
 * 角色守卫
 * 
 * @description
 * 用于实现基于角色的访问控制(RBAC)
 * 检查用户是否具有访问特定路由所需的角色
 * 
 * @module RolesGuard
 */

import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRole } from '../entities/user.entity';

/**
 * 角色装饰器的元数据键
 */
export const ROLES_KEY = 'roles';

/**
 * 角色装饰器
 * 用于标记需要特定角色才能访问的路由
 * @param roles 允许访问的角色列表
 */
export const Roles = (...roles: UserRole[]) => {
  return (target: any, key?: string | symbol, descriptor?: any) => {
    const reflector = new Reflector();
    reflector.set(ROLES_KEY, roles, descriptor.value);
    return descriptor;
  };
};

@Injectable()
export class RolesGuard implements CanActivate {
  /**
   * 构造函数
   * @param reflector 反射器，用于获取路由元数据
   */
  constructor(private reflector: Reflector) {}

  /**
   * 判断请求是否可以通过角色验证
   * @param context 执行上下文
   * @returns 是否允许请求通过
   * @throws ForbiddenException 当用户没有所需角色时
   */
  canActivate(context: ExecutionContext): boolean {
    // 获取路由所需的角色
    const requiredRoles = this.reflector.getAllAndOverride<UserRole[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // 如果路由没有指定所需角色，则允许访问
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    // 获取请求中的用户信息
    const { user } = context.switchToHttp().getRequest();

    // 如果没有用户信息，拒绝访问
    if (!user) {
      throw new ForbiddenException('未找到用户信息');
    }

    // 如果用户是管理员，允许访问所有资源
    if (user.role === UserRole.ADMIN) {
      return true;
    }

    // 检查用户是否具有所需角色
    const hasRole = requiredRoles.includes(user.role);
    if (!hasRole) {
      throw new ForbiddenException('权限不足');
    }

    return true;
  }
} 