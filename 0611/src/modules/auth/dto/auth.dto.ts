/**
 * 认证相关的数据传输对象(DTO)
 * 
 * @description
 * 定义认证过程中使用的数据结构，包括：
 * 1. 登录请求数据
 * 2. 注册请求数据
 * 3. 修改密码请求数据
 * 4. 认证响应数据
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEmail, MinLength, MaxLength, IsOptional, Matches } from 'class-validator';

/**
 * 登录请求DTO
 * @class LoginDto
 */
export class LoginDto {
  /**
   * 用户名
   * @example "john_doe"
   */
  @ApiProperty({ example: 'john_doe', description: '用户名' })
  @IsString()
  @MinLength(4)
  @MaxLength(20)
  username: string;

  /**
   * 密码
   * @example "Password123!"
   */
  @ApiProperty({ example: 'Password123!', description: '密码' })
  @IsString()
  @MinLength(8)
  @MaxLength(32)
  password: string;
}

/**
 * 注册请求DTO
 * @class RegisterDto
 */
export class RegisterDto {
  /**
   * 用户名
   * @example "john_doe"
   */
  @ApiProperty({ example: 'john_doe', description: '用户名' })
  @IsString()
  @MinLength(4)
  @MaxLength(20)
  @Matches(/^[a-zA-Z0-9_-]*$/, {
    message: '用户名只能包含字母、数字、下划线和连字符'
  })
  username: string;

  /**
   * 密码
   * @example "Password123!"
   */
  @ApiProperty({ example: 'Password123!', description: '密码' })
  @IsString()
  @MinLength(8)
  @MaxLength(32)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/, {
    message: '密码必须包含大小写字母、数字和特殊字符'
  })
  password: string;

  /**
   * 邮箱
   * @example "<EMAIL>"
   */
  @ApiProperty({ example: '<EMAIL>', description: '邮箱' })
  @IsEmail()
  email: string;

  /**
   * 手机号
   * @example "13800138000"
   * @optional
   */
  @ApiProperty({ example: '13800138000', description: '手机号', required: false })
  @IsOptional()
  @Matches(/^1[3-9]\d{9}$/, {
    message: '请输入有效的手机号'
  })
  phone?: string;

  /**
   * 真实姓名
   * @example "张三"
   * @optional
   */
  @ApiProperty({ example: '张三', description: '真实姓名', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  realName?: string;
}

/**
 * 修改密码请求DTO
 * @class ChangePasswordDto
 */
export class ChangePasswordDto {
  /**
   * 当前密码
   * @example "OldPassword123!"
   */
  @ApiProperty({ example: 'OldPassword123!', description: '当前密码' })
  @IsString()
  currentPassword: string;

  /**
   * 新密码
   * @example "NewPassword123!"
   */
  @ApiProperty({ example: 'NewPassword123!', description: '新密码' })
  @IsString()
  @MinLength(8)
  @MaxLength(32)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/, {
    message: '密码必须包含大小写字母、数字和特殊字符'
  })
  newPassword: string;
}

/**
 * 认证响应DTO
 * @class AuthResponseDto
 */
export class AuthResponseDto {
  /**
   * 访问令牌
   * @example "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
   */
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: '访问令牌'
  })
  accessToken: string;

  /**
   * 令牌类型
   * @example "Bearer"
   */
  @ApiProperty({ example: 'Bearer', description: '令牌类型' })
  tokenType: string;

  /**
   * 过期时间（秒）
   * @example 86400
   */
  @ApiProperty({ example: 86400, description: '过期时间（秒）' })
  expiresIn: number;
} 