import { Controller, Get, Post, Body, Param, Query, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiQuery, ApiTags } from '@nestjs/swagger';
import { UserQueryDto } from '../dto/user-query.dto';
import { UsersService } from '../services/users.service';
import { RequirePermissions } from '../../auth/decorators/require-permissions.decorator';

@ApiTags('users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  /**
   * 获取用户列表
   */
  @Get()
  @RequirePermissions('user:read')
  @ApiOperation({
    summary: '获取用户列表',
    description: '获取用户列表，支持分页、筛选和排序'
  })
  @ApiQuery({ type: UserQueryDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        items: {
          type: 'array',
          items: { $ref: '#/components/schemas/UserResponseDto' }
        },
        total: { type: 'number', description: '总记录数' },
        page: { type: 'number', description: '当前页码' },
        pageSize: { type: 'number', description: '每页数量' },
        totalPages: { type: 'number', description: '总页数' }
      }
    }
  })
  async findAll(@Query() query: UserQueryDto) {
    const [users, total] = await this.usersService.findAll(query);
    const totalPages = Math.ceil(total / query.pageSize);

    return {
      items: users,
      total,
      page: query.page,
      pageSize: query.pageSize,
      totalPages
    };
  }
}