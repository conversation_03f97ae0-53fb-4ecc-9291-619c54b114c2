/**
 * 用户服务
 * 
 * @description
 * 提供用户管理相关的业务逻辑，包括：
 * 1. 用户CRUD操作
 * 2. 用户查询和过滤
 * 3. 用户权限管理
 * 4. 用户状态管理
 * 
 * @module UsersService
 */

import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, FindOptionsWhere } from 'typeorm';
import { User, UserRole, UserStatus } from '../../auth/entities/user.entity';
import { CreateUserDto, UpdateUserDto, UserQueryDto } from '../dto/user.dto';
import { Permissions } from '../../auth/guards/permissions.guard';

@Injectable()
export class UsersService {
  /**
   * 构造函数
   * @param userRepository 用户仓库
   */
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  /**
   * 创建用户
   * @param createUserDto 创建用户数据
   * @returns 创建的用户信息
   * @throws ConflictException 当用户名或邮箱已存在时
   */
  async create(createUserDto: CreateUserDto): Promise<User> {
    // 检查用户名是否已存在
    const existingUsername = await this.userRepository.findOne({
      where: { username: createUserDto.username },
    });
    if (existingUsername) {
      throw new ConflictException('用户名已存在');
    }

    // 检查邮箱是否已存在
    const existingEmail = await this.userRepository.findOne({
      where: { email: createUserDto.email },
    });
    if (existingEmail) {
      throw new ConflictException('邮箱已被使用');
    }

    // 如果提供了手机号，检查是否已存在
    if (createUserDto.phone) {
      const existingPhone = await this.userRepository.findOne({
        where: { phone: createUserDto.phone },
      });
      if (existingPhone) {
        throw new ConflictException('手机号已被使用');
      }
    }

    // 创建新用户
    const user = this.userRepository.create({
      ...createUserDto,
      status: UserStatus.ACTIVE,
    });

    // 保存用户
    return this.userRepository.save(user);
  }

  /**
   * 更新用户信息
   * @param id 用户ID
   * @param updateUserDto 更新用户数据
   * @returns 更新后的用户信息
   * @throws NotFoundException 当用户不存在时
   * @throws ConflictException 当邮箱或手机号已被其他用户使用时
   */
  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    // 查找用户
    const user = await this.userRepository.findOne({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 如果更新邮箱，检查是否已被使用
    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingEmail = await this.userRepository.findOne({
        where: { email: updateUserDto.email },
      });
      if (existingEmail) {
        throw new ConflictException('邮箱已被使用');
      }
    }

    // 如果更新手机号，检查是否已被使用
    if (updateUserDto.phone && updateUserDto.phone !== user.phone) {
      const existingPhone = await this.userRepository.findOne({
        where: { phone: updateUserDto.phone },
      });
      if (existingPhone) {
        throw new ConflictException('手机号已被使用');
      }
    }

    // 更新用户信息
    Object.assign(user, updateUserDto);
    return this.userRepository.save(user);
  }

  /**
   * 删除用户
   * @param id 用户ID
   * @throws NotFoundException 当用户不存在时
   */
  async remove(id: string): Promise<void> {
    const result = await this.userRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException('用户不存在');
    }
  }

  /**
   * 查找单个用户
   * @param id 用户ID
   * @returns 用户信息
   * @throws NotFoundException 当用户不存在时
   */
  async findOne(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    return user;
  }

  /**
   * 查询用户列表
   */
  async findAll(query: UserQueryDto): Promise<[User[], number]> {
    const {
      page = 1,
      pageSize = 10,
      username,
      email,
      phone,
      role,
      status,
      createdAtStart,
      createdAtEnd,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = query;

    const queryBuilder = this.userRepository.createQueryBuilder('user');

    // 添加查询条件
    if (username) {
      queryBuilder.andWhere('user.username LIKE :username', { username: `%${username}%` });
    }
    if (email) {
      queryBuilder.andWhere('user.email LIKE :email', { email: `%${email}%` });
    }
    if (phone) {
      queryBuilder.andWhere('user.phone LIKE :phone', { phone: `%${phone}%` });
    }
    if (role) {
      queryBuilder.andWhere('user.role = :role', { role });
    }
    if (status) {
      queryBuilder.andWhere('user.status = :status', { status });
    }
    if (createdAtStart) {
      queryBuilder.andWhere('user.createdAt >= :createdAtStart', { createdAtStart });
    }
    if (createdAtEnd) {
      queryBuilder.andWhere('user.createdAt <= :createdAtEnd', { createdAtEnd });
    }

    // 添加排序
    queryBuilder.orderBy(`user.${sortBy}`, sortOrder);

    // 分页
    queryBuilder.skip((page - 1) * pageSize).take(pageSize);

    return queryBuilder.getManyAndCount();
  }

  /**
   * 更新用户状态
   * @param id 用户ID
   * @param status 新状态
   * @returns 更新后的用户信息
   */
  async updateStatus(id: string, status: UserStatus): Promise<User> {
    const user = await this.findOne(id);
    user.status = status;
    return this.userRepository.save(user);
  }

  /**
   * 更新用户权限
   * @param id 用户ID
   * @param permissions 新权限列表
   * @returns 更新后的用户信息
   */
  async updatePermissions(id: string, permissions: string[]): Promise<User> {
    const user = await this.findOne(id);
    user.permissions = permissions;
    return this.userRepository.save(user);
  }

  /**
   * 检查用户是否有指定权限
   * @param userId 用户ID
   * @param permission 权限标识
   * @returns 是否有权限
   */
  async hasPermission(userId: string, permission: string): Promise<boolean> {
    const user = await this.findOne(userId);
    return user.hasPermission(permission);
  }
} 