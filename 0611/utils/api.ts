/**
 * API 工具模块
 * 提供统一的 API 调用接口
 */

export const api = {
  async upload(file: File, type: string) {
    const formData = new FormData()
    formData.append("file", file)
    
    try {
      const response = await fetch(`/api/upload/${type}`, {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        throw new Error("Upload failed")
      }

      const data = await response.json()
      return data.url
    } catch (error) {
      console.error(`Upload ${type} error:`, error)
      throw error
    }
  }
}

export default api 