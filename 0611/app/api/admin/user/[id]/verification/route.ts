import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取用户认证资料
 *
 * @route GET /api/admin/user/[id]/verification
 * @access 需要管理员权限
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id
    logger.info(`[认证资料查看API] 开始处理请求，用户ID: ${userId}`)

    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "认证资料查看API")
    if (response) {
      return response
    }

    // 获取用户认证资料
    logger.debug(`[认证资料查看API] 开始查询用户认证资料: ${userId}`)
    let verification;
    try {
      verification = await prisma.userVerification.findUnique({
        where: { userId }
      })

      logger.debug(`[认证资料查看API] 查询结果:`, verification ? `成功, 类型: ${verification.type}, 状态: ${verification.status}` : '未找到认证资料')

      if (!verification) {
        logger.warn(`用户 ${userId} 没有提交认证资料`)
        return NextResponse.json({
          success: false,
          message: '未找到认证资料，用户可能尚未提交',
          data: null
        }, { status: 404 })
      }
    } catch (dbError) {
      logger.error(`[认证资料查看API] 查询认证资料错误:`, dbError)
      return NextResponse.json({
        success: false,
        message: '查询认证资料时发生错误: ' + (dbError instanceof Error ? dbError.message : '未知错误')
      }, { status: 500 })
    }

    // 构建响应数据
    const responseData = {
      success: true,
      message: '获取认证资料成功',
      data: {
        id: verification.id,
        userId: verification.userId,
        type: verification.type,
        status: verification.status,
        idNumber: verification.idNumber,
        realName: verification.realName,
        frontImageUrl: verification.frontImageUrl,
        backImageUrl: verification.backImageUrl,
        businessLicense: verification.businessLicense,
        companyName: verification.companyName,
        reason: verification.reason,
        submittedAt: verification.submittedAt,
        reviewedAt: verification.reviewedAt,
        reviewedBy: verification.reviewedBy
      }
    }

    logger.debug(`[认证资料查看API] 成功获取用户 ${userId} 的认证资料，类型: ${verification.type}, 状态: ${verification.status}`)

    // 返回认证资料
    return NextResponse.json(responseData)
  } catch (error) {
    logger.error("获取用户认证资料错误:", error)
    return NextResponse.json({
      success: false,
      message: '获取用户认证资料失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
