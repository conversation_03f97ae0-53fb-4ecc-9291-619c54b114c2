/**
 * 策略类型
 */
export interface Policy {
  /**
   * 策略ID
   */
  id: string

  /**
   * 策略名称
   */
  name: string

  /**
   * 策略编码
   */
  code: string

  /**
   * 资源类型
   */
  resourceType: string

  /**
   * 资源标识
   */
  resource: string

  /**
   * 操作类型
   */
  action: string

  /**
   * 策略描述
   */
  description?: string

  /**
   * 是否启用
   */
  enabled: boolean

  /**
   * 创建时间
   */
  createdAt: Date

  /**
   * 更新时间
   */
  updatedAt: Date
}

/**
 * 策略检查请求
 */
export interface CheckPermissionRequest {
  /**
   * 用户信息
   */
  user: {
    /**
     * 用户ID
     */
    id: string

    /**
     * 用户角色
     */
    role: {
      /**
       * 角色编码
       */
      code: string

      /**
       * 角色权限
       */
      permissions: Policy[]
    }
  }

  /**
   * 资源标识
   */
  resource: string

  /**
   * 操作类型
   */
  action: string
}

/**
 * 策略检查响应
 */
export interface CheckPermissionResponse {
  /**
   * 是否允许访问
   */
  allowed: boolean

  /**
   * 拒绝原因
   */
  reason?: string
} 