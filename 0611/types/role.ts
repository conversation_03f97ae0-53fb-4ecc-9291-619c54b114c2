/**
 * 资源类型
 */
export type ResourceType =
  | "menu"
  | "button"
  | "page"
  | "api"
  | "feature"
  | "data"

/**
 * 操作类型
 */
export type ActionType =
  | "view"
  | "create"
  | "update"
  | "delete"
  | "manage"
  | "approve"
  | "export"
  | "import"

/**
 * 权限接口
 */
export interface Permission {
  id: string
  name: string
  code: string
  resourceType: ResourceType
  resource: string
  action: ActionType
  description?: string
  menuItems?: string[]
  createdAt: Date
  updatedAt: Date
}

/**
 * 角色接口
 */
export interface Role {
  id: string
  name: string
  code: string
  type: "system" | "custom"
  description?: string
  permissions: Permission[]
  createdAt: Date
  updatedAt: Date
  isPreset?: boolean
  menuItems?: string[]
}

/**
 * 用户角色
 */
export enum UserRole {
  ADMIN = "ADMIN",
  USER = "USER",
}