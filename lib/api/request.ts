// API基础URL - 前端API请求始终使用本地API路由
// NEXT_PUBLIC_API_BASE_URL 仅用于服务器端代理到外部API
const API_BASE_URL = "/api"

/**
 * API请求工具模块
 */

/**
 * 通用请求方法
 * @param endpoint API端点路径，不包含基础URL
 * @param options 请求选项，包括方法、请求体、请求头等
 * @returns Promise<T> 返回请求结果
 * @throws Error 当请求失败时抛出错误
 */
export async function request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`
  const headers = new Headers(options.headers)
  headers.set("Content-Type", "application/json")

  const response = await fetch(url, {
    ...options,
    headers,
    credentials: 'include',
  })

  if (!response.ok) {
    const error = await response.json().catch(() => ({}))
    throw new Error(error.message || "请求失败")
  }

  return response.json()
}

/**
 * GET请求
 */
export function get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
  const queryString = params
    ? `?${new URLSearchParams(params).toString()}`
    : ""
  return request<T>(`${endpoint}${queryString}`, { method: "GET" })
}

/**
 * POST请求
 */
export function post<T>(endpoint: string, data?: any): Promise<T> {
  return request<T>(endpoint, {
    method: "POST",
    body: JSON.stringify(data),
  })
}

/**
 * PUT请求
 */
export function put<T>(endpoint: string, data?: any): Promise<T> {
  return request<T>(endpoint, {
    method: "PUT",
    body: JSON.stringify(data),
  })
}

/**
 * DELETE请求
 */
export function del<T>(endpoint: string): Promise<T> {
  return request<T>(endpoint, { method: "DELETE" })
}

/**
 * 上传文件
 */
export async function uploadFile<T>(endpoint: string, file: File, data?: Record<string, any>): Promise<T> {
  const formData = new FormData()
  formData.append("file", file)
  
  if (data) {
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, String(value))
      }
    })
  }

  const response = await fetch(`/api${endpoint}`, {
    method: "POST",
    credentials: 'include',
    body: formData,
  })

  if (!response.ok) {
    const error = await response.json().catch(() => ({}))
    throw new Error(error.message || "上传失败")
  }

  return response.json()
}

