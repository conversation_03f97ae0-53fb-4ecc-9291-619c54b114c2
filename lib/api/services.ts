/**
 * API服务模块
 * 封装所有与后端的API交互
 */
import { get, post, put, del, uploadFile } from "./request"
import type {
  ApiResponse,
  LoginParams,
  LoginResponse,
  UserInfo,
  Task,
  TaskCreateParams,
  Customer,
  RechargeRecord,
  PaginatedData,
  PaginationParams,
} from "./interfaces"

// ==================== 用户认证相关API ====================

/**
 * 用户登录（本地认证）
 */
export async function login(params: LoginParams): Promise<ApiResponse<LoginResponse>> {
  try {
    const response = await fetch("/api/auth/login", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(params),
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || "登录失败");
    }

    return response.json();
  } catch (error) {
    console.error("登录失败:", error);
    throw error;
  }
}

/**
 * 用户登出（本地认证）
 */
export async function logout(): Promise<ApiResponse<null>> {
  try {
    const response = await fetch("/api/auth/logout", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || "登出失败");
    }

    return response.json();
  } catch (error) {
    console.error("登出失败:", error);
    throw error;
  }
}

/**
 * 获取当前用户信息（本地认证）
 */
export async function getCurrentUser(): Promise<ApiResponse<UserInfo>> {
  try {
    const response = await fetch("/api/auth/current-user", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || "请求失败");
    }

    return response.json();
  } catch (error) {
    console.error("获取当前用户失败:", error);
    throw error;
  }
}

// ==================== 任务管理相关API ====================

/**
 * 获取任务列表
 */
export function getTasks(
  params: PaginationParams & {
    search?: string
    status?: string
    type?: string
    startDate?: string
    endDate?: string
  },
): Promise<ApiResponse<PaginatedData<Task>>> {
  return get<ApiResponse<PaginatedData<Task>>>("/tasks", params)
}

/**
 * 获取任务详情
 */
export function getTaskById(id: string): Promise<ApiResponse<Task>> {
  return get<ApiResponse<Task>>(`/tasks/${id}`)
}

/**
 * 创建任务
 */
export function createTask(data: TaskCreateParams): Promise<ApiResponse<Task>> {
  return post<ApiResponse<Task>>("/tasks", data)
}

/**
 * 更新任务状态
 */
export function updateTaskStatus(id: string, status: string): Promise<ApiResponse<null>> {
  return put<ApiResponse<null>>(`/tasks/${id}/status`, { status })
}

/**
 * 删除任务
 */
export function deleteTask(id: string): Promise<ApiResponse<null>> {
  return del<ApiResponse<null>>(`/tasks/${id}`)
}

/**
 * 批量导入任务
 */
export function importTasks(
  file: File,
  options?: { type?: string; resource?: string },
): Promise<ApiResponse<{ imported: number; failed: number }>> {
  return uploadFile<ApiResponse<{ imported: number; failed: number }>>("/tasks/import", file, options)
}

// ==================== 客户管理相关API ====================

/**
 * 获取客户列表
 */
export function getCustomers(
  params: PaginationParams & {
    search?: string
    status?: string
  },
): Promise<ApiResponse<PaginatedData<Customer>>> {
  return get<ApiResponse<PaginatedData<Customer>>>("/customers", params)
}

/**
 * 获取客户详情
 */
export function getCustomerById(id: string): Promise<ApiResponse<Customer>> {
  return get<ApiResponse<Customer>>(`/customers/${id}`)
}

/**
 * 创建客户
 */
export function createCustomer(data: Partial<Customer>): Promise<ApiResponse<Customer>> {
  return post<ApiResponse<Customer>>("/customers", data)
}

/**
 * 更新客户信息
 */
export function updateCustomer(id: string, data: Partial<Customer>): Promise<ApiResponse<Customer>> {
  return put<ApiResponse<Customer>>(`/customers/${id}`, data)
}

/**
 * 停用客户账户
 */
export function disableCustomer(id: string, reason: string): Promise<ApiResponse<null>> {
  return post<ApiResponse<null>>(`/customers/${id}/disable`, { reason })
}

/**
 * 为客户账户充值
 */
export function rechargeCustomer(
  id: string,
  amount: number,
  paymentMethod: string,
  remarks?: string,
): Promise<ApiResponse<{ balance: number }>> {
  return post<ApiResponse<{ balance: number }>>(`/customers/${id}/recharge`, { amount, paymentMethod, remarks })
}

/**
 * 获取客户充值记录
 */
export function getCustomerRechargeHistory(
  id: string,
  params: PaginationParams & {
    startDate?: string
    endDate?: string
    type?: string
  },
): Promise<ApiResponse<PaginatedData<RechargeRecord>>> {
  return get<ApiResponse<PaginatedData<RechargeRecord>>>(`/customers/${id}/recharge-history`, params)
}

