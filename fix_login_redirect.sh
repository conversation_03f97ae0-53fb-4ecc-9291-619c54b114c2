#!/bin/bash

# 修复DevBox上的登录重定向问题

echo "正在修复DevBox上的登录重定向问题..."

# 创建临时修复文件
cat > /tmp/login_fix.sed << 'EOF'
/window\.location\.href = result\.url;/c\
            // 修复URL问题：确保使用正确的域名\
            let redirectUrl = result.url;\
            \
            // 如果URL包含localhost，替换为当前域名\
            if (redirectUrl.includes('localhost:3000')) {\
              const currentOrigin = window.location.origin;\
              redirectUrl = redirectUrl.replace('http://localhost:3000', currentOrigin);\
              console.log('修正重定向URL:', redirectUrl);\
            }\
            \
            // 如果URL是相对路径，使用当前域名构建完整URL\
            if (redirectUrl.startsWith('/')) {\
              redirectUrl = `${window.location.origin}${redirectUrl}`;\
              console.log('构建完整URL:', redirectUrl);\
            }\
            \
            window.location.href = redirectUrl;
/router\.push\('\/dashboard'\);/c\
            // 使用当前域名构建dashboard URL\
            const dashboardUrl = `${window.location.origin}/dashboard`;\
            console.log('跳转到dashboard:', dashboardUrl);\
            window.location.href = dashboardUrl;
EOF

# 应用修复
ssh hzh.sealos.run_ns-rzavvov7_web5g 'cd ~/5gweb && sed -f /tmp/login_fix.sed app/\(auth\)/login/components/LoginForm.tsx > app/\(auth\)/login/components/LoginForm.tsx.new && mv app/\(auth\)/login/components/LoginForm.tsx.new app/\(auth\)/login/components/LoginForm.tsx'

echo "修复完成！"
