"use client"

import { useEffect } from "react"
import { secureConsole } from "@/lib/console-security"

/**
 * 系统初始化组件
 * 在客户端加载时执行系统初始化操作
 */
export function SystemInitializer() {
  useEffect(() => {
    const initSystem = async () => {
      try {
        // 只调用基础初始化API，不包含权限刷新
        const response = await fetch("/api/init")
        const data = await response.json()

        if (data.success) {
          secureConsole.log("系统初始化成功")
        } else {
          secureConsole.error("系统初始化失败:", data.message)
        }
      } catch (error) {
        secureConsole.error("系统初始化请求失败:", error)
      }
    }

    // 只在客户端执行一次基础初始化
    initSystem()
  }, [])

  // 这个组件不渲染任何内容
  return null
}
