import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import logger from "@/lib/utils/logger";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";

/**
 * 批量更新所有任务进度
 * 用于管理员手动触发更新所有任务的进度
 */
export async function POST(request: Request) {
  try {
    // 使用权限检查中间件检查用户是否已登录且是管理员
    const { response, user } = await AuthMiddleware.requireAuth(request, "更新所有任务进度API");
    if (response) {
      return response;
    }

    // 检查用户是否有管理员权限
    const isAdmin = user.roleCode?.toLowerCase() === "admin" || user.roleCode?.toLowerCase() === "super";
    if (!isAdmin) {
      return NextResponse.json(
        {
          success: false,
          message: "没有权限执行此操作，需要管理员权限",
          data: null,
        },
        { status: 403 }
      );
    }

    logger.log("开始批量更新所有任务进度...");

    // 解析请求体，获取过滤条件
    const body = await request.json();
    const { status, minProgress, maxProgress } = body;

    // 构建查询条件
    const where: any = {};
    
    // 如果指定了状态，添加状态过滤条件
    if (status) {
      where.status = status;
    }
    
    // 如果指定了最小和最大进度，添加进度范围过滤条件
    if (minProgress !== undefined || maxProgress !== undefined) {
      where.progress = {};
      if (minProgress !== undefined) {
        where.progress.gte = minProgress;
      }
      if (maxProgress !== undefined) {
        where.progress.lte = maxProgress;
      }
    }

    // 查找符合条件的任务
    const tasksToUpdate = await db.task.findMany({ where });

    logger.log(`找到 ${tasksToUpdate.length} 个符合条件的任务`);

    if (tasksToUpdate.length === 0) {
      return NextResponse.json({
        success: true,
        message: "没有符合条件的任务需要更新",
        data: {
          updatedCount: 0
        }
      });
    }

    // 批量更新任务进度
    const updatePromises = tasksToUpdate.map(async (task) => {
      // 根据任务状态和开始时间计算合理的进度值
      let progress = task.progress; // 默认保持原进度
      
      // 如果任务状态为"外呼中"且进度为0，设置一个默认进度
      if (task.status === "外呼中" && task.progress === 0) {
        progress = 25; // 默认进度值
        
        if (task.startTime) {
          const now = new Date();
          const startTime = new Date(task.startTime);
          const runningTimeMinutes = (now.getTime() - startTime.getTime()) / (1000 * 60);
          
          // 根据任务运行时间设置不同的进度值
          if (runningTimeMinutes > 60) { // 如果任务运行超过1小时
            progress = 90; // 设置为接近完成
          } else if (runningTimeMinutes > 30) { // 如果任务运行超过30分钟
            progress = 75; // 设置为较高进度
          } else if (runningTimeMinutes > 15) { // 如果任务运行超过15分钟
            progress = 50; // 设置为中等进度
          } else if (runningTimeMinutes > 5) { // 如果任务运行超过5分钟
            progress = 35; // 设置为较低进度
          }
        }
      }
      
      // 如果任务状态为"已完成"，确保进度为100%
      if (task.status === "已完成" && task.progress !== 100) {
        progress = 100;
      }
      
      // 只有当计算的进度与当前进度不同时才更新
      if (progress !== task.progress) {
        return db.task.update({
          where: { id: task.id },
          data: { progress }
        });
      }
      
      return task; // 如果不需要更新，返回原任务
    });

    // 执行所有更新操作
    const results = await Promise.all(updatePromises);
    
    // 计算实际更新的任务数量
    const updatedCount = results.filter(task => task.progress !== tasksToUpdate.find(t => t.id === task.id)?.progress).length;
    
    logger.log(`成功更新了 ${updatedCount} 个任务的进度`);

    return NextResponse.json({
      success: true,
      message: "任务进度批量更新成功",
      data: {
        totalTasks: tasksToUpdate.length,
        updatedCount: updatedCount
      }
    });
  } catch (error) {
    logger.error("批量更新任务进度失败:", error);
    
    return NextResponse.json({
      success: false,
      message: "批量更新任务进度失败",
      error: (error as Error).message
    }, { status: 500 });
  }
}
