import Redis from 'ioredis'

/**
 * Redis配置
 */
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  username: process.env.REDIS_USERNAME || 'default',
  password: process.env.REDIS_PASSWORD,
  retryStrategy: (times: number) => {
    const delay = Math.min(times * 50, 2000)
    return delay
  },
  maxRetriesPerRequest: 3,
  enableReadyCheck: true,
  reconnectOnError: (err) => {
    const targetError = 'READONLY'
    if (err.message.includes(targetError)) {
      return true
    }
    return false
  },
  // 增加连接超时设置
  connectTimeout: 10000,
  // 增加命令超时设置
  commandTimeout: 5000,
  // 禁用自动重连日志
  lazyConnect: true
}

/**
 * Redis客户端实例
 */
let redis: Redis;

// 创建带有错误处理的Redis实例
try {
  redis = new Redis(redisConfig);

  // 监听连接错误
  redis.on('error', (error) => {
    console.error('Redis连接错误:', error.message);
  });

  // 监听连接成功
  redis.on('connect', () => {
    console.log('Redis连接成功');
  });

  // 监听准备就绪
  redis.on('ready', () => {
    console.log('Redis准备就绪');
  });

} catch (error) {
  console.error('Redis初始化错误:', error);
  // 创建一个内存模拟实现，防止程序崩溃
  redis = {
    set: async (key: string, value: string) => {
      console.log();
      return 'OK';
    },
    get: async (key: string) => {
      console.log();
      return null;
    },
    expire: async (key: string, seconds: number) => {
      console.log();
      return 1;
    },
    del: async (key: string) => {
      console.log();
      return 1;
    },
    ping: async () => {
      return 'PONG';
    }
  } as any;
}

export { redis }
