import { NextResponse } from "next/server";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";
import AesUtils from "@/lib/api/crypto";
import logger from '@/lib/utils/logger';

// 安全地获取环境变量 - 使用视频外呼服务的API地址
const API_BASE_URL = process.env.VIDEO_CALL_API_BASE_URL || "https://services.vcrm.vip:8000";
const ORG_CODE = process.env.NEXT_PUBLIC_VIDEO_CALL_ORG_CODE || "XUNMENGdorg";
const LOGIN_NAME = process.env.NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME || "XUNMENG001";
const APP_ID = process.env.NEXT_PUBLIC_VIDEO_CALL_APP_ID || "";

/**
 * 处理CORS预检请求
 */
export async function OPTIONS(request: Request) {
  const response = new NextResponse(null, { status: 204 });

  // 添加CORS头
  const origin = request.headers.get('origin');
  if (origin) {
    response.headers.set('Access-Control-Allow-Origin', origin);
  } else {
    response.headers.set('Access-Control-Allow-Origin', '*');
  }
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Auth-Timestamp, X-Auth-Signature, X-Auth-App-Id, X-Auth-Login-Name, X-Auth-Org-Code');
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  response.headers.set('Access-Control-Max-Age', '86400'); // 24小时

  return response;
}

/**
 * 代理获取短信模板
 * 解决CORS问题
 */
export async function GET(request: Request) {
  try {
    // 完全禁用认证检查，允许任何请求获取模板
    logger.log("短信模板API: 完全禁用认证检查，允许任何请求获取模板");

    // 记录请求头信息（不包含敏感信息）
    const headersLog: Record<string, string> = {};
    request.headers.forEach((value, key) => {
      if (key.toLowerCase() === 'authorization') {
        headersLog[key] = value ? "Bearer [已隐藏]" : "未提供";
      } else if (key.toLowerCase() === 'cookie') {
        headersLog[key] = "已提供 [内容已隐藏]";
      } else {
        headersLog[key] = value;
      }
    });
    logger.log("短信模板API: 请求头:", headersLog);

    // 获取URL参数
    const url = new URL(request.url);
    const type = url.searchParams.get("type") || "text";
    const isActive = url.searchParams.get("isActive") || "true";
    const pageSize = url.searchParams.get("pageSize") || "100";
    const page = url.searchParams.get("page") || "1";

    // 生成签名
    const timestamp = Date.now();
    const signature = AesUtils.generateSignature(ORG_CODE + timestamp);

    // 构建请求URL - 注意这里使用正确的API路径
    const apiUrl = `${API_BASE_URL}/api/mediaDeliverPlatform/external/getSmsTemplates?type=${type}&isActive=${isActive}&pageSize=${pageSize}&page=${page}`;

    logger.log("短信模板API: 请求URL:", apiUrl);

    // 服务器端发起请求，避免CORS问题
    const apiResponse = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json; charset=utf-8",
        "X-Auth-Timestamp": timestamp.toString(),
        "X-Auth-Signature": signature,
        "X-Auth-App-Id": APP_ID,
        "X-Auth-Login-Name": LOGIN_NAME,
        "X-Auth-Org-Code": ORG_CODE,
      },
      cache: "no-cache"
    });

    if (!apiResponse.ok) {
      logger.error("短信模板API: 请求失败:", apiResponse.status, apiResponse.statusText);
      const errorResponse = NextResponse.json({
        success: false,
        message: `获取短信模板失败: ${apiResponse.status} ${apiResponse.statusText}`,
        data: null,
      }, { status: apiResponse.status });

      // 添加CORS头
      const origin = request.headers.get('origin');
      if (origin) {
        errorResponse.headers.set('Access-Control-Allow-Origin', origin);
      } else {
        errorResponse.headers.set('Access-Control-Allow-Origin', '*');
      }
      errorResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      errorResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      errorResponse.headers.set('Access-Control-Allow-Credentials', 'true');

      return errorResponse;
    }

    const data = await apiResponse.json();
    logger.log("短信模板API: 请求成功, 返回数据:", JSON.stringify(data).substring(0, 200) + "...");

    const response = NextResponse.json({
      success: true,
      message: "获取短信模板成功",
      data: data,
    });

    // 添加CORS头
    const origin = request.headers.get('origin');
    if (origin) {
      response.headers.set('Access-Control-Allow-Origin', origin);
    } else {
      response.headers.set('Access-Control-Allow-Origin', '*');
    }
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    response.headers.set('Access-Control-Allow-Credentials', 'true');

    return response;
  } catch (error) {
    logger.error("短信模板API: 请求异常:", error);
    const errorResponse = NextResponse.json({
      success: false,
      message: "获取短信模板失败，请稍后重试",
      data: null,
    }, { status: 500 });

    // 添加CORS头
    const origin = request.headers.get('origin');
    if (origin) {
      errorResponse.headers.set('Access-Control-Allow-Origin', origin);
    } else {
      errorResponse.headers.set('Access-Control-Allow-Origin', '*');
    }
    errorResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    errorResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    errorResponse.headers.set('Access-Control-Allow-Credentials', 'true');

    return errorResponse;
  }
}
