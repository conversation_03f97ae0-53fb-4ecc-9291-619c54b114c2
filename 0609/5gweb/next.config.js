/** @type {import('next').NextConfig} */
const nextConfig = {

  // 生产环境配置
  productionBrowserSourceMaps: false,

  // 环境变量配置
  env: {
    // 添加客户端可用的环境变量
    APP_ENV: process.env.APP_ENV || 'development',
    API_BASE_URL: process.env.API_BASE_URL || '',
    // 前端API调用指向本地API
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
    // 视频外呼服务API地址
    VIDEO_CALL_API_BASE_URL: process.env.VIDEO_CALL_API_BASE_URL || 'https://services.vcrm.vip:8000',
  },

  // 自定义 headers 配置
  async headers() {
    return [
      // 静态资源缓存策略
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      },
      // 图片缓存策略
      {
        source: '/images/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400, stale-while-revalidate=31536000'
          }
        ]
      },
      // 字体缓存策略
      {
        source: '/fonts/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      },
      // 主页面安全头
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Content-Security-Policy',
            value: process.env.NODE_ENV === 'production'
              ? "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tiny.cloud; style-src 'self' 'unsafe-inline' https://cdn.tiny.cloud; img-src 'self' data: blob: https:; font-src 'self' data: https:; connect-src 'self' https://services.vcrm.vip:8000 https://api.example.com; frame-src 'self' https://cdn.tiny.cloud; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'self';"
              : "",
          },
          {
            key: 'Transfer-Encoding',
            value: 'chunked'
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=(), interest-cohort=()'
          }
        ],
      },
    ];
  },

  // 实验性功能配置
  experimental: {
    largePageDataBytes: 128 * 1000, // 128KB
    serverComponentsExternalPackages: ['bcrypt'],
    outputFileTracingExcludes: {
      '**/*.d.ts': ['**/*.d.ts'],
      '**/*.map': ['**/*.map']
    },
    // 添加这行来减少构建时的网络请求
    skipTrailingSlashRedirect: true,
    skipMiddlewareUrlNormalize: true
  },

  // 编译选项
  swcMinify: true,

  // 图片优化配置
  images: {
    domains: ['localhost', 'services.vcrm.vip'],
    formats: ['image/avif', 'image/webp'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048],
    imageSizes: [16, 32, 48, 64, 96, 128, 256],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: false,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Webpack 配置
  webpack: (config, { isServer, webpack }) => {
    if (!isServer) {
      // 在客户端构建中，将 process 替换为空对象
      config.resolve.fallback = {
        ...config.resolve.fallback,
        process: require.resolve('process/browser'),
      };

      // 添加 ProvidePlugin 以在客户端代码中提供 process
      config.plugins.push(
        new webpack.ProvidePlugin({
          process: 'process/browser',
        })
      );
    }

    return config;
  },
  typescript: {
    // 在生产构建时忽略 TypeScript 错误
    ignoreBuildErrors: true,
  },
  eslint: {
    // 在生产构建时忽略 ESLint 错误
    ignoreDuringBuilds: true,
  },
}

module.exports = nextConfig