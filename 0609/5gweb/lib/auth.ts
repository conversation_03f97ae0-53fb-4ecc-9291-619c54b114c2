import { NextAuthOptions, User } from "next-auth"
import { getServerSession } from "next-auth/next"
import CredentialsProvider from "next-auth/providers/credentials"
import { prisma } from "@/lib/prisma"
import { compare } from "bcryptjs"
import { verify } from 'jsonwebtoken'
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';
import logger from '@/lib/utils/logger';

// 定义JWT密钥
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars';

export interface TokenPayload {
  id: string;
  username: string;
  roleCode: string;
  exp: number;
}

export async function getToken(request?: NextRequest): Promise<TokenPayload | null> {
  try {
    // 首先尝试从请求中获取令牌
    if (request) {
      logger.log('尝试从请求中获取令牌');

      // 1. 尝试从Authorization头获取
      const authHeader = request.headers.get('authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const headerToken = authHeader.substring(7);
        logger.log('从Authorization头获取到令牌');
        try {
          const payload = verify(headerToken, JWT_SECRET) as any;
          return {
            id: payload.sub || payload.userId as string,
            username: payload.username as string,
            roleCode: payload.roleCode as string,
            exp: payload.exp as number
          };
        } catch (tokenError) {
          logger.error('Authorization头令牌验证失败:', tokenError);
          // 继续尝试其他方法
        }
      }

      // 2. 尝试从cookie获取
      const cookieHeader = request.headers.get('cookie');
      if (cookieHeader) {
        logger.log('从请求cookie中查找令牌');
        const cookies = cookieHeader.split(';');

        // 首先尝试获取NextAuth会话cookie
        for (const cookie of cookies) {
          const [name, value] = cookie.trim().split('=');
          if (name === 'next-auth.session-token' || name === '__Secure-next-auth.session-token') {
            logger.log(`找到NextAuth会话cookie: ${name}`);

            // NextAuth会话cookie不需要验证，直接尝试获取会话
            try {
              // 这里我们不直接验证cookie值，而是让NextAuth的getServerSession处理
              // 在UnifiedAuthService中会处理这种情况
              logger.log('发现NextAuth会话cookie，将在UnifiedAuthService中处理');
              return null; // 返回null，让UnifiedAuthService继续处理
            } catch (sessionError) {
              logger.error('处理NextAuth会话cookie时出错:', sessionError);
            }
          }
        }

        // 然后尝试获取自定义token cookie
        for (const cookie of cookies) {
          const [name, value] = cookie.trim().split('=');
          if (name === 'token') {
            logger.log(`从cookie ${name} 获取到令牌`);
            try {
              const payload = verify(value, JWT_SECRET) as any;
              return {
                id: payload.sub || payload.userId as string,
                username: payload.username as string,
                roleCode: payload.roleCode as string,
                exp: payload.exp as number
              };
            } catch (tokenError) {
              logger.error(`Cookie ${name} 令牌验证失败:`, tokenError);
              // 继续尝试其他方法
            }
          }
        }
      }

      // 3. 尝试从请求体获取令牌
      try {
        const clonedRequest = request.clone();
        const contentType = request.headers.get('content-type') || '';

        // 只有当内容类型为JSON时才尝试解析
        if (contentType.includes('application/json')) {
          const body = await clonedRequest.json();
          if (body.token) {
            logger.log('从请求体获取到令牌');
            try {
              const payload = verify(body.token, JWT_SECRET) as any;
              return {
                id: payload.sub || payload.userId as string,
                username: payload.username as string,
                roleCode: payload.roleCode as string,
                exp: payload.exp as number
              };
            } catch (tokenError) {
              logger.error('请求体令牌验证失败:', tokenError);
            }
          } else {
            logger.log('请求体中没有token字段');
          }
        } else {
          logger.log(`请求内容类型不是JSON: ${contentType}`);
        }
      } catch (bodyError) {
        logger.error('解析请求体失败:', bodyError);
      }
    }

    // 如果从请求中获取失败，尝试从服务器端cookie获取
    logger.log('尝试从服务器端cookie获取令牌');
    const cookieStore = cookies();

    // 首先尝试获取NextAuth会话cookie
    const nextAuthCookie = cookieStore.get('next-auth.session-token') ||
                          cookieStore.get('__Secure-next-auth.session-token');
    if (nextAuthCookie) {
      logger.log('找到NextAuth会话cookie');
      // 这里我们不直接验证cookie值，而是让NextAuth的getServerSession处理
      // 在UnifiedAuthService中会处理这种情况
      return null; // 返回null，让UnifiedAuthService继续处理
    }

    // 然后尝试获取自定义token cookie
    const token = cookieStore.get('token')?.value;
    if (!token) {
      logger.log('未找到令牌');
      return null;
    }

    logger.log('从服务器端cookie获取到令牌');
    try {
      const payload = verify(token, JWT_SECRET) as any;
      return {
        id: payload.sub || payload.userId as string,
        username: payload.username as string,
        roleCode: payload.roleCode as string,
        exp: payload.exp as number
      };
    } catch (tokenError) {
      logger.error('服务器端cookie令牌验证失败:', tokenError);
      return null;
    }
  } catch (error) {
    logger.error('Token verification failed:', error);
    return null;
  }
}

export const authOptions: NextAuthOptions = {
  pages: {
    signIn: "/login",
    error: "/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production", // 生产环境使用 HTTPS，开发环境使用 HTTP
        domain: process.env.NODE_ENV === "production" ?
          (process.env.NEXTAUTH_URL ? new URL(process.env.NEXTAUTH_URL).hostname : undefined) :
          undefined,
      },
    },
  },
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "邮箱", type: "email" },
        password: { label: "密码", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("请输入邮箱和密码");
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
          include: {
            role: {
              include: {
                menus: true,
              },
            },
          },
        });

        if (!user) {
          throw new Error("用户不存在");
        }

        const isValid = await compare(credentials.password, user.password);
        if (!isValid) {
          logger.log("Invalid password for user:", credentials?.email);
          throw new Error("密码错误");
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          username: user.username,
          image: user.image,
          roleCode: user.roleCode,
        } as any;
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        const u = user as any;
        logger.log('JWT回调 - 用户信息:', u);
        const newToken = {
          ...token,
          id: u.id,
          username: u.username,
          email: u.email,
          name: u.name,
          image: u.image,
          roleCode: u.roleCode,
        };
        logger.log('JWT回调 - 新令牌:', newToken);
        return newToken;
      }
      return token;
    },
    async session({ session, token }) {
      logger.log('会话回调 - 令牌:', token);
      const newSession = {
        ...session,
        user: {
          ...session.user,
          id: token.id as string,
          username: token.username as string,
          email: token.email as string,
          name: token.name as string | null,
          image: token.image as string | null,
          roleCode: token.roleCode as string,
        }
      };
      logger.log('会话回调 - 新会话:', newSession);
      return newSession;
    },
  },
}

// 获取当前用户信息
export async function getCurrentUser() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return null;
    }
    return session.user;
  } catch (error) {
    logger.error("Error getting current user:", error);
    return null;
  }
}

/**
 * 验证JWT令牌
 * @param token JWT令牌
 * @returns 解析后的载荷数据
 */
export async function verifyToken(token: string) {
  try {
    logger.log('开始验证令牌:', token.substring(0, 20) + '...');
    const payload = verify(token, JWT_SECRET);
    logger.log('令牌验证成功, 载荷:', payload);
    return payload;
  } catch (error) {
    logger.error('令牌验证失败:', error);
    throw error;
  }
}