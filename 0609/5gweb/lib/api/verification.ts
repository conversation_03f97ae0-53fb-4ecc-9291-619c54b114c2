import Redis from 'ioredis'

/**
 * Redis 配置接口
 */
interface RedisConfig {
  host: string
  port: number
  password?: string
}

/**
 * 验证码配置接口
 */
interface VerificationConfig {
  prefix: string
  expireSeconds: number
  codeLength: number
}

/**
 * 默认 Redis 配置
 */
const defaultRedisConfig: RedisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  username: process.env.REDIS_USERNAME || 'default',
  password: process.env.REDIS_PASSWORD
}

/**
 * 默认验证码配置
 */
const defaultVerificationConfig: VerificationConfig = {
  prefix: 'verification_code:',
  expireSeconds: 5 * 60, // 5分钟
  codeLength: 6
}

/**
 * 验证服务类
 * 提供验证码生成、存储和验证功能
 */
class VerificationService {
  private redis: Redis
  private config: VerificationConfig

  /**
   * 构造函数
   * @param redisConfig - Redis 配置，可选
   * @param verificationConfig - 验证码配置，可选
   */
  constructor(
    redisConfig: RedisConfig = defaultRedisConfig,
    verificationConfig: VerificationConfig = defaultVerificationConfig
  ) {
    this.redis = new Redis(redisConfig)
    this.config = verificationConfig
  }

  /**
   * 生成指定长度的随机验证码
   * @returns string 生成的验证码
   * 
   * @example
   * ```typescript
   * const verificationService = new VerificationService()
   * const code = verificationService.generateCode()
   * console.log('生成的验证码:', code) // 例如: '123456'
   * ```
   */
  generateCode(): string {
    return Math.random().toString().slice(2, 2 + this.config.codeLength)
  }

  /**
   * 存储验证码
   * @param email - 用户邮箱
   * @param code - 验证码
   * @returns Promise<void>
   * @throws 如果存储失败，将抛出错误
   * 
   * @example
   * ```typescript
   * const verificationService = new VerificationService()
   * try {
   *   await verificationService.storeCode('<EMAIL>', '123456')
   *   console.log('验证码存储成功')
   * } catch (error) {
   *   console.error('验证码存储失败:', error)
   * }
   * ```
   */
  async storeCode(email: string, code: string): Promise<void> {
    const key = `${this.config.prefix}${email}`
    try {
      await this.redis.set(key, code, 'EX', this.config.expireSeconds)
    } catch (error) {
      console.error('存储验证码失败:', error)
      throw error
    }
  }

  /**
   * 验证验证码
   * @param email - 用户邮箱
   * @param code - 待验证的验证码
   * @returns Promise<boolean> 验证结果
   * @throws 如果验证过程出错，将抛出错误
   * 
   * @example
   * ```typescript
   * const verificationService = new VerificationService()
   * try {
   *   const isValid = await verificationService.verifyCode('<EMAIL>', '123456')
   *   if (isValid) {
   *     console.log('验证码验证成功')
   *   } else {
   *     console.log('验证码无效或已过期')
   *   }
   * } catch (error) {
   *   console.error('验证码验证失败:', error)
   * }
   * ```
   */
  async verifyCode(email: string, code: string): Promise<boolean> {
    const key = `${this.config.prefix}${email}`
    try {
      const storedCode = await this.redis.get(key)
      
      if (!storedCode || storedCode !== code) {
        return false
      }

      // 验证成功后删除验证码
      await this.redis.del(key)
      return true
    } catch (error) {
      console.error('验证码验证失败:', error)
      throw error
    }
  }

  /**
   * 关闭 Redis 连接
   * @returns Promise<void>
   */
  async close(): Promise<void> {
    await this.redis.quit()
  }
}

// 导出默认的验证服务实例
const verificationService = new VerificationService()
export default verificationService

// 导出类型定义和配置接口
export type { RedisConfig, VerificationConfig } 