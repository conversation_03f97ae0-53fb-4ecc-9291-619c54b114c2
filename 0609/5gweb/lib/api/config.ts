/**
 * 视频外呼API配置
 */
// 视频外呼服务的API base URL (外部服务)
const API_BASE_URL = process.env.VIDEO_CALL_API_BASE_URL || process.env.NEXT_PUBLIC_API_BASE_URL || "https://services.vcrm.vip:8000"

// Organization code from environment variable
const ORG_CODE = process.env.NEXT_PUBLIC_VIDEO_CALL_ORG_CODE || ""

// Login name from environment variable
const LOGIN_NAME = process.env.NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME || ""

// App ID (provided by the API service)
const APP_ID = process.env.NEXT_PUBLIC_VIDEO_CALL_APP_ID || ""

// AES key and IV for encryption
const AES_KEY = process.env.NEXT_PUBLIC_VIDEO_CALL_AES_KEY || ""
const AES_IV = process.env.NEXT_PUBLIC_VIDEO_CALL_AES_IV || ""

export const VIDEO_CALL_API_CONFIG = {
  baseUrl: API_BASE_URL,
  orgCode: ORG_CODE,
  loginName: LOGIN_NAME,
  aesKey: AES_KEY,
  aesIv: AES_IV,
  appId: APP_ID,
}

export default VIDEO_CALL_API_CONFIG

