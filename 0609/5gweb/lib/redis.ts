import logger from '@/lib/utils/logger';

import Redis from 'ioredis'

/**
 * Redis配置
 */
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  username: process.env.REDIS_USERNAME || 'default',
  password: process.env.REDIS_PASSWORD,
  retryStrategy: (times: number) => {
    const delay = Math.min(times * 50, 2000)
    return delay
  },
  maxRetriesPerRequest: 3,
  enableReadyCheck: true,
  reconnectOnError: (err) => {
    const targetError = 'READONLY'
    if (err.message.includes(targetError)) {
      return true
    }
    return false
  },
  // 增加连接超时设置
  connectTimeout: 10000,
  // 增加命令超时设置
  commandTimeout: 5000,
  // 禁用自动重连日志
  lazyConnect: true
}

/**
 * Redis客户端实例
 */
let redis: Redis;

// 创建带有错误处理的Redis实例
try {
  redis = new Redis(redisConfig);

  // 监听连接错误
  redis.on('error', (error) => {
    logger.error('Redis连接错误:', error.message);
  });

  // 监听连接成功
  redis.on('connect', () => {
    logger.log('Redis连接成功');
  });

  // 监听准备就绪
  redis.on('ready', () => {
    logger.log('Redis准备就绪');
  });

  // 测试Redis连接
  redis.ping().then(() => {
    logger.log('Redis服务器响应正常');
  }).catch((err) => {
    logger.error('Redis服务器无法响应:', err.message);
  });

} catch (error) {
  logger.error('Redis初始化错误:', error);
  // 创建一个内存模拟实现，防止程序崩溃
  redis = {
    set: async (key: string, value: string) => {
      logger.log();
      return 'OK';
    },
    get: async (key: string) => {
      logger.log();
      // 模拟验证码逻辑，始终返回123456
      if (key.startsWith('verification:')) {
        return '123456';
      }
      return null;
    },
    expire: async (key: string, seconds: number) => {
      logger.log();
      return 1;
    },
    del: async (key: string) => {
      logger.log();
      return 1;
    },
    ping: async () => {
      return 'PONG';
    }
  } as any;
}

export { redis }
